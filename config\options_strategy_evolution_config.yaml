# 🧬 Options Strategy Evolution Agent Configuration
# Comprehensive configuration for adaptive strategy optimization

# ═══════════════════════════════════════════════════════════════════════════════
# 🧪 GENETIC ALGORITHM PARAMETERS
# ═══════════════════════════════════════════════════════════════════════════════
genetic_algorithm:
  population_size: 50              # Number of strategies in population
  generations: 100                 # Maximum generations to evolve
  mutation_rate: 0.15             # Probability of mutation (15%)
  crossover_rate: 0.8             # Probability of crossover (80%)
  selection_pressure: 0.3         # Selection pressure (30% survive)
  elite_percentage: 0.1           # Elite strategies preserved (10%)
  diversity_threshold: 0.7        # Minimum diversity to maintain

# ═══════════════════════════════════════════════════════════════════════════════
# 📊 PERFORMANCE THRESHOLDS
# ═══════════════════════════════════════════════════════════════════════════════
performance_thresholds:
  min_roi: 0.02                   # 2% minimum ROI
  min_sharpe: 0.2                 # Minimum Sharpe ratio
  min_win_rate: 0.45              # 45% minimum win rate
  max_drawdown: 0.25              # 25% maximum drawdown
  min_trades: 20                  # Minimum trades for evaluation
  min_expectancy: 0.02            # Minimum expectancy
  min_profit_factor: 1.2          # Minimum profit factor
  min_calmar_ratio: 0.3           # Minimum Calmar ratio

# ═══════════════════════════════════════════════════════════════════════════════
# ⏰ EVOLUTION INTERVALS (seconds)
# ═══════════════════════════════════════════════════════════════════════════════
evolution_intervals:
  performance_check: 10           # 10 seconds - Check strategy performance
  regime_adaptation: 30           # 30 seconds - Adapt to market regime
  diversity_maintenance: 60       # 1 minute - Maintain diversity
  full_evolution: 120             # 2 minutes - Full evolution cycle
  registry_cleanup: 300           # 5 minutes - Registry maintenance
  experiment_evaluation: 60       # 1 minute - Evaluate experiments
  learning_update: 300            # 5 minutes - Update learning parameters

# ═══════════════════════════════════════════════════════════════════════════════
# 🧪 MUTATION PARAMETERS
# ═══════════════════════════════════════════════════════════════════════════════
mutation_parameters:
  # Technical indicator ranges
  rsi_range: [5, 25]              # RSI period range
  ma_range: [5, 50]               # Moving average period range
  bollinger_range: [10, 30]      # Bollinger bands period range
  
  # Risk management ranges
  stop_loss_range: [0.005, 0.05] # 0.5% to 5% stop loss
  take_profit_range: [0.01, 0.10] # 1% to 10% take profit
  position_size_range: [0.01, 0.1] # 1% to 10% position size
  
  # Options-specific ranges
  iv_rank_range: [10, 90]         # IV rank filter range
  delta_range: [0.1, 0.9]         # Delta range for options
  theta_range: [-0.1, 0.1]        # Theta range
  gamma_range: [0.01, 0.5]        # Gamma range
  
  # Time-based parameters
  timeframe_options:              # Available timeframes
    - "1min"
    - "3min"
    - "5min"
    - "15min"
    
  
  expiry_days_range: [7, 30]      # Days to expiry range
  
  # Market condition filters
  volume_multiplier_range: [0.8, 2.0] # Volume vs average
  volatility_range: [0.1, 0.5]   # Volatility range
  trend_strength_range: [0.3, 0.9] # Trend strength

# ═══════════════════════════════════════════════════════════════════════════════
# 🌊 MARKET REGIME DETECTION
# ═══════════════════════════════════════════════════════════════════════════════
market_regime:
  volatility_thresholds:
    low: 0.15                     # Below 15% volatility
    high: 0.25                    # Above 25% volatility
  
  trend_thresholds:
    weak: 0.3                     # Below 30% trend strength
    strong: 0.7                   # Above 70% trend strength
  
  volume_thresholds:
    low: 0.8                      # Below 80% of average
    high: 1.5                     # Above 150% of average
  
  regime_adaptation:
    trending_bull:
      preferred_strategies: ["long_call", "bull_call_spread"]
      mutation_bias: "aggressive_profit_targeting"
    
    trending_bear:
      preferred_strategies: ["long_put", "bear_put_spread"]
      mutation_bias: "defensive_risk_management"
    
    sideways_low_vol:
      preferred_strategies: ["iron_condor", "butterfly"]
      mutation_bias: "theta_optimization"
    
    sideways_high_vol:
      preferred_strategies: ["short_straddle", "short_strangle"]
      mutation_bias: "volatility_selling"
    
    volatile_uncertain:
      preferred_strategies: ["long_straddle", "long_strangle"]
      mutation_bias: "volatility_buying"

# ═══════════════════════════════════════════════════════════════════════════════
# 🔬 EXPERIMENTATION FRAMEWORK
# ═══════════════════════════════════════════════════════════════════════════════
experimentation:
  max_concurrent_experiments: 15   # Maximum concurrent experiments
  experiment_duration_days: 7     # Days to run each experiment
  min_sample_size: 20             # Minimum trades for statistical significance
  confidence_level: 0.95          # Statistical confidence level
  
  a_b_testing:
    control_group_size: 0.5       # 50% control group
    test_group_size: 0.5          # 50% test group
    early_stopping_threshold: 0.1 # Stop if 10% performance difference
  
  experiment_types:
    - "parameter_optimization"
    - "regime_adaptation"
    - "risk_management_tuning"
    - "entry_timing_optimization"
    - "exit_strategy_testing"

# ═══════════════════════════════════════════════════════════════════════════════
# 🔄 SELF-LEARNING PARAMETERS
# ═══════════════════════════════════════════════════════════════════════════════
self_learning:
  learning_rate: 0.01             # Learning rate for parameter updates
  memory_window_days: 30          # Days of history to consider
  adaptation_threshold: 0.05      # Minimum improvement to adapt
  
  reinforcement_learning:
    enabled: true
    reward_function: "sharpe_weighted_return"
    exploration_rate: 0.1         # Epsilon for exploration
    discount_factor: 0.95         # Gamma for future rewards
  
  pattern_recognition:
    enabled: true
    min_pattern_occurrences: 5    # Minimum occurrences to recognize pattern
    pattern_confidence: 0.8       # Confidence threshold for patterns

# ═══════════════════════════════════════════════════════════════════════════════
# 📧 NOTIFICATION SETTINGS
# ═══════════════════════════════════════════════════════════════════════════════
notifications:
  email_enabled: false            # Enable email notifications
  telegram_enabled: false        # Enable Telegram notifications
  
  email_config:
    smtp_server: "smtp.gmail.com"
    smtp_port: 587
    username: ""                  # Your email username
    password: ""                  # Your email password (use app password)
    recipients: []                # List of recipient emails
  
  telegram_config:
    bot_token: ""                 # Telegram bot token
    chat_ids: []                  # List of chat IDs to notify
  
  notification_triggers:
    strategy_promoted: true       # Notify when strategy is promoted
    strategy_demoted: true        # Notify when strategy is demoted
    regime_change: true           # Notify on market regime change
    evolution_completed: true     # Notify when evolution cycle completes
    performance_alert: true       # Notify on performance issues
    experiment_results: true      # Notify on experiment completion

# ═══════════════════════════════════════════════════════════════════════════════
# ═══════════════════════════════════════════════════════════════════════════════
# ⚡ PERFORMANCE SETTINGS
# ═══════════════════════════════════════════════════════════════════════════════
performance:
  gpu_acceleration: true          # Enable GPU acceleration with CuPy if available

# 🗂️ STRATEGY REGISTRY SETTINGS
# ═══════════════════════════════════════════════════════════════════════════════
strategy_registry:
  max_versions_per_strategy: 10   # Maximum versions to keep
  archive_after_days: 90          # Archive strategies after 90 days
  cleanup_disabled_after_days: 30 # Remove disabled strategies after 30 days
  
  version_control:
    auto_versioning: true         # Automatic version numbering
    semantic_versioning: false    # Use semantic versioning (v1.2.3)
    branch_naming: "evolution"    # Branch name for evolution
  
  metadata_tracking:
    track_lineage: true           # Track strategy lineage
    track_performance: true       # Track performance metrics
    track_regime_performance: true # Track regime-specific performance

# ═══════════════════════════════════════════════════════════════════════════════
# 🪟 WINDOWS-SPECIFIC SETTINGS
# ═══════════════════════════════════════════════════════════════════════════════
windows_optimization:
  use_multiprocessing: true      # Use multiprocessing for parallel evolution
  max_workers: 8                 # Maximum worker processes
  memory_limit_mb: 7120          # Memory limit per process (MB)
  
  file_handling:
    use_async_io: true            # Use async file I/O
    buffer_size: 8192             # File buffer size
    temp_directory: "temp"        # Temporary directory for processing
  
  performance:
    enable_jit: true             # Enable JIT compilation (if available)
    optimize_polars: true         # Use Polars optimizations
    use_pyarrow_backend: true     # Use PyArrow backend for Polars

# ═══════════════════════════════════════════════════════════════════════════════
# 📊 POLARS/PYARROW CONFIGURATION
# ═══════════════════════════════════════════════════════════════════════════════
data_processing:
  polars_config:
    streaming: true               # Enable streaming for large datasets
    lazy_evaluation: true         # Use lazy evaluation
    parallel_execution: true      # Enable parallel execution
    
  pyarrow_config:
    memory_pool: "system"         # Memory pool type
    compression: "snappy"         # Compression algorithm
    
  technical_indicators:
    use_polars_talib: true        # Use polars-talib if available
    fallback_to_custom: true      # Fallback to custom implementations
    cache_indicators: true        # Cache calculated indicators

# ═══════════════════════════════════════════════════════════════════════════════
# 🎯 ENSEMBLE STRATEGY SETTINGS
# ═══════════════════════════════════════════════════════════════════════════════
ensemble_strategies:
  enabled: true                   # Enable ensemble strategy creation
  min_strategies_for_ensemble: 3  # Minimum strategies to create ensemble
  max_ensemble_size: 5            # Maximum strategies in ensemble
  
  combination_methods:
    - "weighted_average"          # Weight by performance
    - "majority_voting"           # Majority vote on signals
    - "confidence_weighted"       # Weight by signal confidence
    - "regime_specific"           # Different strategies per regime
  
  performance_weighting:
    sharpe_weight: 0.4            # Weight for Sharpe ratio
    return_weight: 0.3            # Weight for returns
    consistency_weight: 0.3       # Weight for consistency

# ═══════════════════════════════════════════════════════════════════════════════
# 🔍 LOGGING AND MONITORING
# ═══════════════════════════════════════════════════════════════════════════════
logging:
  level: "INFO"                   # Logging level
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  
  file_logging:
    enabled: true
    filename: "logs/strategy_evolution.log"
    max_size_mb: 100              # Maximum log file size
    backup_count: 5               # Number of backup files
  
  performance_logging:
    log_all_mutations: false      # Log every mutation (verbose)
    log_performance_changes: true # Log performance changes
    log_regime_changes: true      # Log regime changes
    
  evolution_tracking:
    detailed_lineage: true        # Track detailed strategy lineage
    mutation_history: true        # Track mutation history
    performance_attribution: true # Track performance attribution
