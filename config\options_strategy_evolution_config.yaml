# 🧬 Options Strategy Evolution Agent Configuration
# Comprehensive configuration for adaptive strategy optimization

# ═══════════════════════════════════════════════════════════════════════════════
# 🧪 GENETIC ALGORITHM PARAMETERS - OPTIMIZED FOR HIGH RESOURCE USAGE
# ═══════════════════════════════════════════════════════════════════════════════
genetic_algorithm:
  population_size: 150             # Increased from 50 - More strategies in population for better diversity
  generations: 200                 # Increased from 100 - More generations for better evolution
  mutation_rate: 0.20             # Increased from 0.15 - Higher mutation rate for faster adaptation
  crossover_rate: 0.85            # Increased from 0.8 - More crossover for better exploration
  selection_pressure: 0.25        # Reduced from 0.3 - Less pressure for more diversity
  elite_percentage: 0.15          # Increased from 0.1 - Preserve more elite strategies
  diversity_threshold: 0.6        # Reduced from 0.7 - Allow more similar strategies for intensive testing

# ═══════════════════════════════════════════════════════════════════════════════
# 📊 PERFORMANCE THRESHOLDS
# ═══════════════════════════════════════════════════════════════════════════════
performance_thresholds:
  min_roi: 0.02                   # 2% minimum ROI
  min_sharpe: 0.2                 # Minimum Sharpe ratio
  min_win_rate: 0.45              # 45% minimum win rate
  max_drawdown: 0.25              # 25% maximum drawdown
  min_trades: 20                  # Minimum trades for evaluation
  min_expectancy: 0.02            # Minimum expectancy
  min_profit_factor: 1.2          # Minimum profit factor
  min_calmar_ratio: 0.3           # Minimum Calmar ratio

# ═══════════════════════════════════════════════════════════════════════════════
# ⏰ EVOLUTION INTERVALS (seconds) - AGGRESSIVE TESTING INTERVALS
# ═══════════════════════════════════════════════════════════════════════════════
evolution_intervals:
  performance_check: 5            # 5 seconds - More frequent performance checks
  regime_adaptation: 15           # 15 seconds - Faster regime adaptation
  diversity_maintenance: 30       # 30 seconds - More frequent diversity maintenance
  full_evolution: 60              # 1 minute - More frequent full evolution cycles
  registry_cleanup: 180           # 3 minutes - More frequent cleanup
  experiment_evaluation: 30       # 30 seconds - Faster experiment evaluation
  learning_update: 120            # 2 minutes - More frequent learning updates

# ═══════════════════════════════════════════════════════════════════════════════
# 🧪 MUTATION PARAMETERS
# ═══════════════════════════════════════════════════════════════════════════════
mutation_parameters:
  # Technical indicator ranges
  rsi_range: [5, 25]              # RSI period range
  ma_range: [5, 50]               # Moving average period range
  bollinger_range: [10, 30]      # Bollinger bands period range
  
  # Risk management ranges
  stop_loss_range: [0.005, 0.05] # 0.5% to 5% stop loss
  take_profit_range: [0.01, 0.10] # 1% to 10% take profit
  position_size_range: [0.01, 0.1] # 1% to 10% position size
  
  # Options-specific ranges
  iv_rank_range: [10, 90]         # IV rank filter range
  delta_range: [0.1, 0.9]         # Delta range for options
  theta_range: [-0.1, 0.1]        # Theta range
  gamma_range: [0.01, 0.5]        # Gamma range
  
  # Time-based parameters
  timeframe_options:              # Available timeframes
    - "1min"
    - "3min"
    - "5min"
    - "15min"
    
  
  expiry_days_range: [7, 30]      # Days to expiry range
  
  # Market condition filters
  volume_multiplier_range: [0.8, 2.0] # Volume vs average
  volatility_range: [0.1, 0.5]   # Volatility range
  trend_strength_range: [0.3, 0.9] # Trend strength

# ═══════════════════════════════════════════════════════════════════════════════
# 🌊 MARKET REGIME DETECTION
# ═══════════════════════════════════════════════════════════════════════════════
market_regime:
  volatility_thresholds:
    low: 0.15                     # Below 15% volatility
    high: 0.25                    # Above 25% volatility
  
  trend_thresholds:
    weak: 0.3                     # Below 30% trend strength
    strong: 0.7                   # Above 70% trend strength
  
  volume_thresholds:
    low: 0.8                      # Below 80% of average
    high: 1.5                     # Above 150% of average
  
  regime_adaptation:
    trending_bull:
      preferred_strategies: ["long_call", "bull_call_spread"]
      mutation_bias: "aggressive_profit_targeting"
    
    trending_bear:
      preferred_strategies: ["long_put", "bear_put_spread"]
      mutation_bias: "defensive_risk_management"
    
    sideways_low_vol:
      preferred_strategies: ["iron_condor", "butterfly"]
      mutation_bias: "theta_optimization"
    
    sideways_high_vol:
      preferred_strategies: ["short_straddle", "short_strangle"]
      mutation_bias: "volatility_selling"
    
    volatile_uncertain:
      preferred_strategies: ["long_straddle", "long_strangle"]
      mutation_bias: "volatility_buying"

# ═══════════════════════════════════════════════════════════════════════════════
# 🔬 EXPERIMENTATION FRAMEWORK - HIGH THROUGHPUT TESTING
# ═══════════════════════════════════════════════════════════════════════════════
experimentation:
  max_concurrent_experiments: 50   # Increased from 15 - More concurrent experiments
  experiment_duration_days: 3     # Reduced from 7 - Faster experiment cycles
  min_sample_size: 15             # Reduced from 20 - Faster statistical significance
  confidence_level: 0.90          # Reduced from 0.95 - Faster decisions with slightly lower confidence
  
  a_b_testing:
    control_group_size: 0.4       # 40% control group - More resources for testing
    test_group_size: 0.6          # 60% test group - More aggressive testing
    early_stopping_threshold: 0.05 # Stop if 5% performance difference - Faster decisions
  
  experiment_types:
    - "parameter_optimization"
    - "regime_adaptation"
    - "risk_management_tuning"
    - "entry_timing_optimization"
    - "exit_strategy_testing"

# ═══════════════════════════════════════════════════════════════════════════════
# 🔄 SELF-LEARNING PARAMETERS
# ═══════════════════════════════════════════════════════════════════════════════
self_learning:
  learning_rate: 0.01             # Learning rate for parameter updates
  memory_window_days: 30          # Days of history to consider
  adaptation_threshold: 0.05      # Minimum improvement to adapt
  
  reinforcement_learning:
    enabled: true
    reward_function: "sharpe_weighted_return"
    exploration_rate: 0.1         # Epsilon for exploration
    discount_factor: 0.95         # Gamma for future rewards
  
  pattern_recognition:
    enabled: true
    min_pattern_occurrences: 5    # Minimum occurrences to recognize pattern
    pattern_confidence: 0.8       # Confidence threshold for patterns

# ═══════════════════════════════════════════════════════════════════════════════
# 📧 NOTIFICATION SETTINGS
# ═══════════════════════════════════════════════════════════════════════════════
notifications:
  email_enabled: false            # Enable email notifications
  telegram_enabled: false        # Enable Telegram notifications
  
  email_config:
    smtp_server: "smtp.gmail.com"
    smtp_port: 587
    username: ""                  # Your email username
    password: ""                  # Your email password (use app password)
    recipients: []                # List of recipient emails
  
  telegram_config:
    bot_token: ""                 # Telegram bot token
    chat_ids: []                  # List of chat IDs to notify
  
  notification_triggers:
    strategy_promoted: true       # Notify when strategy is promoted
    strategy_demoted: true        # Notify when strategy is demoted
    regime_change: true           # Notify on market regime change
    evolution_completed: true     # Notify when evolution cycle completes
    performance_alert: true       # Notify on performance issues
    experiment_results: true      # Notify on experiment completion

# ═══════════════════════════════════════════════════════════════════════════════
# ⚡ PERFORMANCE SETTINGS - MAXIMUM RESOURCE UTILIZATION
# ═══════════════════════════════════════════════════════════════════════════════
performance:
  gpu_acceleration: true          # Enable GPU acceleration with CuPy if available
  cpu_intensive_mode: true        # Enable CPU-intensive processing mode
  memory_aggressive_mode: true    # Use aggressive memory allocation
  parallel_backtesting: true      # Run multiple backtests in parallel
  concurrent_strategy_limit: 100  # Maximum concurrent strategies being processed
  batch_processing_size: 50       # Process strategies in larger batches

  resource_targets:
    target_cpu_usage: 85          # Target 85% CPU usage
    target_memory_usage_gb: 6     # Target 6GB memory usage
    max_memory_usage_gb: 7        # Maximum 7GB memory usage before throttling

  optimization_flags:
    enable_numba_jit: true        # Enable Numba JIT compilation
    enable_cython_extensions: true # Use Cython extensions if available
    vectorized_operations: true   # Prefer vectorized operations
    memory_pool_enabled: true     # Use memory pooling for frequent allocations

# 🗂️ STRATEGY REGISTRY SETTINGS
# ═══════════════════════════════════════════════════════════════════════════════
strategy_registry:
  max_versions_per_strategy: 10   # Maximum versions to keep
  archive_after_days: 90          # Archive strategies after 90 days
  cleanup_disabled_after_days: 30 # Remove disabled strategies after 30 days
  
  version_control:
    auto_versioning: true         # Automatic version numbering
    semantic_versioning: false    # Use semantic versioning (v1.2.3)
    branch_naming: "evolution"    # Branch name for evolution
  
  metadata_tracking:
    track_lineage: true           # Track strategy lineage
    track_performance: true       # Track performance metrics
    track_regime_performance: true # Track regime-specific performance

# ═══════════════════════════════════════════════════════════════════════════════
# 🪟 WINDOWS-SPECIFIC SETTINGS - HIGH RESOURCE UTILIZATION
# ═══════════════════════════════════════════════════════════════════════════════
windows_optimization:
  use_multiprocessing: true      # Use multiprocessing for parallel evolution
  max_workers: 16                # Increased from 8 - More worker processes for higher CPU usage
  memory_limit_mb: 6144          # Increased from 7120 - Higher memory per process (6GB total target)
  cpu_affinity_enabled: true     # Enable CPU affinity for better performance
  priority_class: "high"         # Set high priority for the process

  file_handling:
    use_async_io: true            # Use async file I/O
    buffer_size: 65536            # Increased from 8192 - Larger buffer for better I/O performance
    temp_directory: "temp"        # Temporary directory for processing
    concurrent_file_ops: 8        # Number of concurrent file operations

  performance:
    enable_jit: true             # Enable JIT compilation (if available)
    optimize_polars: true         # Use Polars optimizations
    use_pyarrow_backend: true     # Use PyArrow backend for Polars
    enable_vectorization: true    # Enable vectorized operations
    parallel_strategy_evaluation: true # Evaluate strategies in parallel

# ═══════════════════════════════════════════════════════════════════════════════
# 📊 POLARS/PYARROW CONFIGURATION - HIGH PERFORMANCE DATA PROCESSING
# ═══════════════════════════════════════════════════════════════════════════════
data_processing:
  polars_config:
    streaming: true               # Enable streaming for large datasets
    lazy_evaluation: true         # Use lazy evaluation
    parallel_execution: true      # Enable parallel execution
    thread_pool_size: 16          # Increased thread pool for parallel operations
    memory_map: true              # Use memory mapping for large files

  pyarrow_config:
    memory_pool: "system"         # Memory pool type
    compression: "lz4"            # Changed from snappy - Faster compression for high throughput
    batch_size: 65536             # Larger batch size for better performance

  technical_indicators:
    use_polars_talib: true        # Use polars-talib if available
    fallback_to_custom: true      # Fallback to custom implementations
    cache_indicators: true        # Cache calculated indicators
    parallel_indicator_calc: true # Calculate indicators in parallel
    indicator_batch_size: 10000   # Batch size for indicator calculations

# ═══════════════════════════════════════════════════════════════════════════════
# 🎯 ENSEMBLE STRATEGY SETTINGS
# ═══════════════════════════════════════════════════════════════════════════════
ensemble_strategies:
  enabled: true                   # Enable ensemble strategy creation
  min_strategies_for_ensemble: 3  # Minimum strategies to create ensemble
  max_ensemble_size: 5            # Maximum strategies in ensemble
  
  combination_methods:
    - "weighted_average"          # Weight by performance
    - "majority_voting"           # Majority vote on signals
    - "confidence_weighted"       # Weight by signal confidence
    - "regime_specific"           # Different strategies per regime
  
  performance_weighting:
    sharpe_weight: 0.4            # Weight for Sharpe ratio
    return_weight: 0.3            # Weight for returns
    consistency_weight: 0.3       # Weight for consistency

# ═══════════════════════════════════════════════════════════════════════════════
# 🔍 LOGGING AND MONITORING
# ═══════════════════════════════════════════════════════════════════════════════
logging:
  level: "INFO"                   # Logging level
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  
  file_logging:
    enabled: true
    filename: "logs/strategy_evolution.log"
    max_size_mb: 100              # Maximum log file size
    backup_count: 5               # Number of backup files
  
  performance_logging:
    log_all_mutations: false      # Log every mutation (verbose)
    log_performance_changes: true # Log performance changes
    log_regime_changes: true      # Log regime changes

  evolution_tracking:
    detailed_lineage: true        # Track detailed strategy lineage
    mutation_history: true        # Track mutation history
    performance_attribution: true # Track performance attribution

# ═══════════════════════════════════════════════════════════════════════════════
# 🚀 HIGH THROUGHPUT EVOLUTION SETTINGS
# ═══════════════════════════════════════════════════════════════════════════════
high_throughput_mode:
  enabled: true                   # Enable high throughput evolution mode

  parallel_processing:
    strategy_evaluation_workers: 16 # Workers for strategy evaluation
    backtest_workers: 12          # Workers for backtesting
    mutation_workers: 8           # Workers for mutations
    crossover_workers: 6          # Workers for crossover operations

  memory_management:
    strategy_cache_size: 1000     # Cache up to 1000 strategies in memory
    result_cache_size: 5000       # Cache up to 5000 results
    gc_frequency: 100             # Garbage collection every 100 operations
    memory_cleanup_threshold: 0.9 # Clean memory when 90% full

  batch_operations:
    mutation_batch_size: 50       # Process mutations in batches of 50
    evaluation_batch_size: 100    # Evaluate strategies in batches of 100
    crossover_batch_size: 25      # Process crossovers in batches of 25

  performance_monitoring:
    resource_check_interval: 30   # Check resource usage every 30 seconds
    auto_throttle_enabled: true   # Automatically throttle if resources exceed limits
    performance_logging_interval: 60 # Log performance metrics every minute
