[{"strategy_id": "crossover_f9fa0448", "name": "Strategy LP_NIFTY_24600_20250718192943", "description": "Crossover of Strategy LP_NIFTY_24600_20250718192943 and Strategy LS_NIFTY_25200_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.01649864008394304, "take_profit": 0.053699558283287645}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_d4d602fa", "name": "Strategy LC_NIFTY_24200_20250718192943", "description": "Crossover of Strategy LC_NIFTY_24200_20250718192943 and Strategy LP_NIFTY_24000_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_e49391de", "name": "Strategy LC_NIFTY_24300_20250718192943", "description": "Crossover of Strategy LC_NIFTY_24300_20250718192943 and Strategy LC_NIFTY_25600_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_d631ddfd", "name": "Strategy LC_NIFTY_25500_20250718192943", "description": "Crossover of Strategy LC_NIFTY_25500_20250718192943 and Strategy LC_NIFTY_24200_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_02a75ee4", "name": "Strategy LC_NIFTY_24100_20250718192943", "description": "Crossover of Strategy LC_NIFTY_24100_20250718192943 and Strategy LC_NIFTY_25000_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}]