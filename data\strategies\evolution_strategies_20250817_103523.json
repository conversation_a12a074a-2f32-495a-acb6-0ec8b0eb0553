[{"strategy_id": "crossover_44681992", "name": "Strategy LST_NIFTY_25300_24200_20250718192943", "description": "Crossover of Strategy LST_NIFTY_25300_24200_20250718192943 and Strategy ATMLP_NIFTY_25400_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.01910415200807508, "take_profit": 0.050980558963467676}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_2c8e74f9", "name": "Strategy LS_NIFTY_24400_20250718192943", "description": "Crossover of Strategy LS_NIFTY_24400_20250718192943 and Strategy LS_NIFTY_24500_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.01813646760536314, "take_profit": 0.054429026907597544}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_5e7a6b14", "name": "Strategy LS_NIFTY_25900_20250718192943", "description": "Crossover of Strategy LS_NIFTY_25900_20250718192943 and Strategy LP_NIFTY_25300_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.019554440621422968, "take_profit": 0.05164504218350205}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_70f2e2ba", "name": "Strategy LP_NIFTY_24400_20250718192943", "description": "Crossover of Strategy LP_NIFTY_24400_20250718192943 and Strategy LP_NIFTY_24900_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_305a1009", "name": "Strategy LC_NIFTY_24100_20250718192943", "description": "Crossover of Strategy LC_NIFTY_24100_20250718192943 and Strategy LC_NIFTY_24800_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_2a0194af", "name": "Strategy LP_NIFTY_24400_20250718192943", "description": "Crossover of Strategy LP_NIFTY_24400_20250718192943 and Strategy LC_NIFTY_25200_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_12298eab", "name": "Strategy LC_NIFTY_24400_20250718192943", "description": "Crossover of Strategy LC_NIFTY_24400_20250718192943 and Strategy LC_NIFTY_24800_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_61e24ba6", "name": "Strategy LP_NIFTY_24400_20250718192943", "description": "Crossover of Strategy LP_NIFTY_24400_20250718192943 and Strategy LS_NIFTY_24700_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_6aaa5738", "name": "Strategy LC_NIFTY_25600_20250718192943", "description": "Crossover of Strategy LC_NIFTY_25600_20250718192943 and Strategy LP_NIFTY_25600_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_4cca0412", "name": "Strategy LS_NIFTY_24400_20250718192943", "description": "Crossover of Strategy LS_NIFTY_24400_20250718192943 and Strategy LST_NIFTY_25300_24100_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_42100a02", "name": "Strategy LS_NIFTY_24300_20250718192943", "description": "Crossover of Strategy LS_NIFTY_24300_20250718192943 and Strategy LS_NIFTY_24400_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_05ec4ef3", "name": "Strategy LP_NIFTY_25900_20250718192943", "description": "Crossover of Strategy LP_NIFTY_25900_20250718192943 and Strategy LS_NIFTY_25500_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_dce6c6cc", "name": "Strategy LP_NIFTY_25900_20250718192943", "description": "Crossover of Strategy LP_NIFTY_25900_20250718192943 and Strategy LC_NIFTY_24600_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_51f5c13f", "name": "Strategy LS_NIFTY_25500_20250718192943", "description": "Crossover of Strategy LS_NIFTY_25500_20250718192943 and Strategy ATMLC_NIFTY_25300_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.021001900232545778, "take_profit": 0.05158634805331178}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_02492e5f", "name": "Strategy LP_NIFTY_24500_20250718192943", "description": "Crossover of Strategy LP_NIFTY_24500_20250718192943 and Strategy LC_NIFTY_24900_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_0f9807e8", "name": "Strategy ATMLC_NIFTY_25000_20250718192943", "description": "Crossover of Strategy ATMLC_NIFTY_25000_20250718192943 and Strategy LS_NIFTY_25900_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.019777220310711482, "take_profit": 0.050822521091751026}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_b47a2b68", "name": "Strategy LP_NIFTY_25100_20250718192943", "description": "Crossover of Strategy LP_NIFTY_25100_20250718192943 and Strategy LS_NIFTY_25900_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.020466668636391638, "take_profit": 0.04970356686798266}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_3b3358c8", "name": "Strategy ATMLC_NIFTY_25100_20250718192943", "description": "Crossover of Strategy ATMLC_NIFTY_25100_20250718192943 and Strategy LC_NIFTY_25300_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_4c2b0f2b", "name": "Strategy LP_NIFTY_25100_20250718192943", "description": "Crossover of Strategy LP_NIFTY_25100_20250718192943 and Strategy LC_NIFTY_24400_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_a4611fc2", "name": "Strategy LS_NIFTY_25800_20250718192943", "description": "Crossover of Strategy LS_NIFTY_25800_20250718192943 and Strategy LST_NIFTY_25200_24100_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_129c2bc9", "name": "Strategy ATMLC_NIFTY_25000_20250718192943", "description": "Crossover of Strategy ATMLC_NIFTY_25000_20250718192943 and Strategy ATMLP_NIFTY_24900_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.019888610155355743, "take_profit": 0.05041126054587551}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_634c42c4", "name": "Strategy ATMLC_NIFTY_25100_20250718192943", "description": "Crossover of Strategy ATMLC_NIFTY_25100_20250718192943 and Strategy LC_NIFTY_24400_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_2bf2eb83", "name": "Strategy LP_NIFTY_25800_20250718192943", "description": "Crossover of Strategy LP_NIFTY_25800_20250718192943 and Strategy LS_NIFTY_25100_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.021236320656243847, "take_profit": 0.04602501829553392}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_2615bcd0", "name": "Strategy ATMLP_NIFTY_24800_20250718192943", "description": "Crossover of Strategy ATMLP_NIFTY_24800_20250718192943 and Strategy LP_NIFTY_25900_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_c4b6c9de", "name": "Strategy LC_NIFTY_24400_20250718192943", "description": "Crossover of Strategy LC_NIFTY_24400_20250718192943 and Strategy LS_NIFTY_25900_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_131fe7d6", "name": "Strategy LST_NIFTY_25100_24100_20250718192943", "description": "Crossover of Strategy LST_NIFTY_25100_24100_20250718192943 and Strategy LP_NIFTY_24400_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_1534dd33", "name": "Strategy LS_NIFTY_24700_20250718192943", "description": "Crossover of Strategy LS_NIFTY_24700_20250718192943 and Strategy LC_NIFTY_24900_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_3386826f", "name": "Strategy LC_NIFTY_24900_20250718192943", "description": "Crossover of Strategy LC_NIFTY_24900_20250718192943 and Strategy LP_NIFTY_25900_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_660e416f", "name": "Strategy LP_NIFTY_24400_20250718192943", "description": "Crossover of Strategy LP_NIFTY_24400_20250718192943 and Strategy ATMLC_NIFTY_25500_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.018619274492458704, "take_profit": 0.04596824028703078}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_c2481703", "name": "Strategy LC_NIFTY_24100_20250718192943", "description": "Crossover of Strategy LC_NIFTY_24100_20250718192943 and Strategy LP_NIFTY_24400_20250718192943", "parameters": {}, "entry_conditions": ["delta > 0.3"], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_f64dfcba", "name": "Strategy LP_NIFTY_25900_20250718192943", "description": "Crossover of Strategy LP_NIFTY_25900_20250718192943 and Strategy ATMLC_NIFTY_25100_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_15dcdcaa", "name": "Strategy LST_NIFTY_25100_24000_20250718192943", "description": "Crossover of Strategy LST_NIFTY_25100_24000_20250718192943 and Strategy LS_NIFTY_25100_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_dcc384b9", "name": "Strategy LS_NIFTY_24100_20250718192943", "description": "Crossover of Strategy LS_NIFTY_24100_20250718192943 and Strategy LC_NIFTY_24100_20250718192943", "parameters": {}, "entry_conditions": ["time_to_expiry < 45"], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_aced454e", "name": "Strategy ATMLC_NIFTY_25100_20250718192943", "description": "Crossover of Strategy ATMLC_NIFTY_25100_20250718192943 and Strategy LP_NIFTY_24400_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_c36d8538", "name": "Strategy ATMLP_NIFTY_25500_20250718192943", "description": "Crossover of Strategy ATMLP_NIFTY_25500_20250718192943 and Strategy LC_NIFTY_24600_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_05f1b97a", "name": "Strategy LST_NIFTY_25100_24000_20250718192943", "description": "Crossover of Strategy LST_NIFTY_25100_24000_20250718192943 and Strategy LC_NIFTY_25200_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.01814680784027472, "take_profit": 0.04489220055900407}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_aa712db1", "name": "Strategy LC_NIFTY_25100_20250718192943", "description": "Crossover of Strategy LC_NIFTY_25100_20250718192943 and Strategy LST_NIFTY_25100_24000_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_0082fa03", "name": "Strategy ATMLP_NIFTY_25300_20250718192943", "description": "Crossover of Strategy ATMLP_NIFTY_25300_20250718192943 and Strategy LP_NIFTY_24400_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.020916778415223103, "take_profit": 0.04703096262072979}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_11139c3d", "name": "Strategy LST_NIFTY_25100_24100_20250718192943", "description": "Crossover of Strategy LST_NIFTY_25100_24100_20250718192943 and Strategy LP_NIFTY_25700_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.020079077912145124, "take_profit": 0.05858224310146222}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_42afaf66", "name": "Strategy LP_NIFTY_25800_20250718192943", "description": "Crossover of Strategy LP_NIFTY_25800_20250718192943 and Strategy LC_NIFTY_24100_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.024225165105101223, "take_profit": 0.0439540448888829}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_bcc11a45", "name": "Strategy LS_NIFTY_25900_20250718192943", "description": "Crossover of Strategy LS_NIFTY_25900_20250718192943 and Strategy LS_NIFTY_24000_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.019777220310711482, "take_profit": 0.050822521091751026}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_735c4573", "name": "Strategy LS_NIFTY_25900_20250718192943", "description": "Crossover of Strategy LS_NIFTY_25900_20250718192943 and Strategy LS_NIFTY_25100_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_63998b64", "name": "Strategy ATMLP_NIFTY_25000_20250718192943", "description": "Crossover of Strategy ATMLP_NIFTY_25000_20250718192943 and Strategy LS_NIFTY_25600_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_ef5c34ba", "name": "Strategy LP_NIFTY_25100_20250718192943", "description": "Crossover of Strategy LP_NIFTY_25100_20250718192943 and Strategy LC_NIFTY_25300_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02023333431819582, "take_profit": 0.04985178343399133}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_d2528a3e", "name": "Strategy LS_NIFTY_25000_20250718192943", "description": "Crossover of Strategy LS_NIFTY_25000_20250718192943 and Strategy LP_NIFTY_25400_20250718192943", "parameters": {}, "entry_conditions": ["volume > avg_volume_10 * 1.5"], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_0712b755", "name": "Strategy LC_NIFTY_25600_20250718192943", "description": "Crossover of Strategy LC_NIFTY_25600_20250718192943 and Strategy LS_NIFTY_24300_20250718192943", "parameters": {}, "entry_conditions": ["vix < 25"], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02204892714479951, "take_profit": 0.04414091375219115}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_bac5c347", "name": "Strategy LS_NIFTY_24100_20250718192943", "description": "Crossover of Strategy LS_NIFTY_24100_20250718192943 and Strategy LP_NIFTY_25800_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.020618160328121922, "take_profit": 0.04801250914776696}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_6ec5765f", "name": "Strategy LC_NIFTY_25300_20250718192943", "description": "Crossover of Strategy LC_NIFTY_25300_20250718192943 and Strategy LS_NIFTY_25700_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_8f9d5d05", "name": "Strategy LP_NIFTY_24200_20250718192943", "description": "Crossover of Strategy LP_NIFTY_24200_20250718192943 and Strategy LP_NIFTY_25900_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_0fb51ce9", "name": "Strategy ATMLC_NIFTY_25000_20250718192943", "description": "Crossover of Strategy ATMLC_NIFTY_25000_20250718192943 and Strategy LP_NIFTY_25900_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.019888610155355743, "take_profit": 0.05041126054587551}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_4c83dda4", "name": "Strategy ATMLC_NIFTY_24500_20250718192943", "description": "Crossover of Strategy ATMLC_NIFTY_24500_20250718192943 and Strategy LS_NIFTY_24400_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_fdcfa24c", "name": "Strategy ATMLC_NIFTY_24700_20250718192943", "description": "Crossover of Strategy ATMLC_NIFTY_24700_20250718192943 and Strategy ATMLC_NIFTY_25400_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_286f8549", "name": "Strategy ATMLC_NIFTY_25000_20250718192943", "description": "Crossover of Strategy ATMLC_NIFTY_25000_20250718192943 and Strategy LS_NIFTY_24800_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.019944305077677874, "take_profit": 0.05020563027293776}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_4d942718", "name": "Strategy LP_NIFTY_25400_20250718192943", "description": "Crossover of Strategy LP_NIFTY_25400_20250718192943 and Strategy ATMLC_NIFTY_24600_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_e13ca7cc", "name": "Strategy LC_NIFTY_25600_20250718192943", "description": "Crossover of Strategy LC_NIFTY_25600_20250718192943 and Strategy ATMLC_NIFTY_24500_20250718192943", "parameters": {}, "entry_conditions": ["vix < 25"], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.021236016159106193, "take_profit": 0.04922500859398244}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_2a5f3c1d", "name": "Strategy ATMLC_NIFTY_24500_20250718192943", "description": "Crossover of Strategy ATMLC_NIFTY_24500_20250718192943 and Strategy LP_NIFTY_25400_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_0ac7955a", "name": "Strategy LP_NIFTY_25800_20250718192943", "description": "Crossover of Strategy LP_NIFTY_25800_20250718192943 and Strategy LS_NIFTY_24400_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02211258255255061, "take_profit": 0.04697702244444145}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_2a6e2a8b", "name": "Strategy ATMLP_NIFTY_25100_20250718192943", "description": "Crossover of Strategy ATMLP_NIFTY_25100_20250718192943 and Strategy ATMLP_NIFTY_25400_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_66bb5411", "name": "Strategy LST_NIFTY_25300_24000_20250718192943", "description": "Crossover of Strategy LST_NIFTY_25300_24000_20250718192943 and Strategy LS_NIFTY_24400_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.018217441552242666, "take_profit": 0.05221361651476502}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_3f9c3bce", "name": "Strategy LP_NIFTY_25100_20250718192943", "description": "Crossover of Strategy LP_NIFTY_25100_20250718192943 and Strategy LS_NIFTY_25100_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02011666715909791, "take_profit": 0.04992589171699567}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_9164a495", "name": "Strategy LST_NIFTY_25300_24000_20250718192943", "description": "Crossover of Strategy LST_NIFTY_25300_24000_20250718192943 and Strategy LP_NIFTY_24400_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.01834326681210322, "take_profit": 0.04592329872613039}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_0ae56c1b", "name": "Strategy LP_NIFTY_25000_20250718192943", "description": "Crossover of Strategy LP_NIFTY_25000_20250718192943 and Strategy LC_NIFTY_24800_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02070771850226786, "take_profit": 0.0528320693438114}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_4c6786b8", "name": "Strategy ATMLP_NIFTY_24900_20250718192943", "description": "Crossover of Strategy ATMLP_NIFTY_24900_20250718192943 and Strategy LC_NIFTY_25100_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_006e872c", "name": "Strategy LC_NIFTY_24100_20250718192943", "description": "Crossover of Strategy LC_NIFTY_24100_20250718192943 and Strategy LS_NIFTY_25300_20250718192943", "parameters": {}, "entry_conditions": ["delta > 0.3"], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_c8cda890", "name": "Strategy LS_NIFTY_24700_20250718192943", "description": "Crossover of Strategy LS_NIFTY_24700_20250718192943 and Strategy LC_NIFTY_25100_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.017338631587427385, "take_profit": 0.048577091494376515}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_260cad5d", "name": "Strategy LP_NIFTY_25100_20250718192943", "description": "Crossover of Strategy LP_NIFTY_25100_20250718192943 and Strategy LC_NIFTY_24300_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02011666715909791, "take_profit": 0.04992589171699567}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}]