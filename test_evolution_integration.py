#!/usr/bin/env python3
"""
Test script to verify the integration between the evolution agent and the backtesting system.
This script demonstrates how evolved strategies are exported to config/options_strategies.yaml
and can be used by the backtesting and signal generation agents.
"""

import asyncio
import yaml
import json
import logging
from pathlib import Path
from datetime import datetime, timedelta
from agents.options_strategy_evolution_agent import OptionsStrategyEvolutionAgent, StrategyConfig, StrategyStatus, StrategyMetrics, MarketRegime

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_evolution_integration():
    """Test the complete evolution to production integration flow"""
    
    logger.info("🧪 Starting Evolution Integration Test")
    
    # Initialize evolution agent
    evolution_agent = OptionsStrategyEvolutionAgent()
    
    try:
        # Initialize the agent
        logger.info("📋 Initializing evolution agent...")
        await evolution_agent.initialize()
        
        # Create some test evolved strategies
        logger.info("🧬 Creating test evolved strategies...")
        await create_test_evolved_strategies(evolution_agent)
        
        # Add performance metrics to make them eligible for production
        logger.info("📊 Adding performance metrics...")
        await add_test_performance_metrics(evolution_agent)
        
        # Test strategy summary
        logger.info("📈 Getting evolved strategies summary...")
        summary = await evolution_agent.get_evolved_strategies_summary()
        logger.info(f"Summary: {json.dumps(summary, indent=2, default=str)}")
        
        # Test manual export to production
        logger.info("🔄 Testing manual export to production...")
        success = await evolution_agent.export_strategies_to_production(force=True)
        logger.info(f"Export success: {success}")
        
        # Verify the strategies were added to the YAML file
        logger.info("✅ Verifying strategies in config/options_strategies.yaml...")
        await verify_strategies_in_yaml()
        
        # Test strategy promotion
        logger.info("⬆️ Testing strategy promotion...")
        strategy_ids = list(evolution_agent.strategy_registry.keys())
        if strategy_ids:
            test_strategy_id = strategy_ids[0]
            success = await evolution_agent.promote_strategy_to_production(test_strategy_id, force=True)
            logger.info(f"Promotion success: {success}")
        
        # Test strategy demotion
        logger.info("⬇️ Testing strategy demotion...")
        if strategy_ids:
            test_strategy_id = strategy_ids[0]
            success = await evolution_agent.demote_strategy_from_production(test_strategy_id, "test_demotion")
            logger.info(f"Demotion success: {success}")
        
        # Test how backtesting agent would load these strategies
        logger.info("🔍 Testing backtesting agent integration...")
        await test_backtesting_integration()
        
        logger.info("✅ Evolution Integration Test Completed Successfully!")
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        raise
    finally:
        # Cleanup
        await evolution_agent.cleanup()

async def create_test_evolved_strategies(evolution_agent):
    """Create some test evolved strategies"""
    
    test_strategies = [
        {
            'strategy_id': 'evolved_momentum_call_v1',
            'name': 'Evolved Momentum Call',
            'description': 'Evolved momentum-based call strategy with optimized parameters',
            'parameters': {
                'rsi_threshold': 52,
                'ema_period': 18,
                'confidence_threshold': 0.42,
                'max_signals_per_day': 6
            },
            'entry_conditions': ['rsi_14 > 52', 'underlying_above_ema_18'],
            'exit_conditions': ['profit_target: 0.45', 'stop_loss: 0.25'],
            'risk_management': {
                'max_loss_per_trade': 800,
                'position_size_pct': 0.025,
                'max_concurrent_positions': 2
            },
            'market_outlook': 'bullish',
            'volatility_outlook': 'moderate',
            'timeframe': '5min',
            'status': StrategyStatus.EXPERIMENTAL,
            'parent_id': 'momentum_long_call',
            'version': 'v1.2'
        },
        {
            'strategy_id': 'evolved_volatility_straddle_v1',
            'name': 'Evolved Volatility Straddle',
            'description': 'Evolved straddle strategy with adaptive IV thresholds',
            'parameters': {
                'iv_rank_threshold': 25,
                'delta_range': [0.48, 0.52],
                'confidence_threshold': 0.38,
                'max_signals_per_day': 3
            },
            'entry_conditions': ['iv_rank < 25', 'range_bound: true'],
            'exit_conditions': ['profit_target: 0.35', 'stop_loss: 0.55'],
            'risk_management': {
                'max_loss_per_trade': 1500,
                'position_size_pct': 0.03,
                'max_concurrent_positions': 2
            },
            'market_outlook': 'neutral',
            'volatility_outlook': 'expanding',
            'timeframe': '15min',
            'status': StrategyStatus.EXPERIMENTAL,
            'parent_id': 'long_straddle',
            'version': 'v1.1'
        }
    ]
    
    for strategy_data in test_strategies:
        strategy_config = StrategyConfig(
            strategy_id=strategy_data['strategy_id'],
            name=strategy_data['name'],
            description=strategy_data['description'],
            parameters=strategy_data['parameters'],
            entry_conditions=strategy_data['entry_conditions'],
            exit_conditions=strategy_data['exit_conditions'],
            risk_management=strategy_data['risk_management'],
            market_outlook=strategy_data['market_outlook'],
            volatility_outlook=strategy_data['volatility_outlook'],
            timeframe=strategy_data['timeframe'],
            status=strategy_data['status'],
            parent_id=strategy_data['parent_id'],
            version=strategy_data['version']
        )
        
        evolution_agent.strategy_registry[strategy_data['strategy_id']] = strategy_config
        logger.info(f"Created test strategy: {strategy_data['name']}")

async def add_test_performance_metrics(evolution_agent):
    """Add test performance metrics to make strategies eligible for production"""
    
    for strategy_id in evolution_agent.strategy_registry.keys():
        # Create multiple performance measurements over time
        for i in range(5):
            timestamp = datetime.now() - timedelta(days=i*7)  # Weekly measurements
            
            # Generate good performance metrics
            metrics = StrategyMetrics(
                strategy_id=strategy_id,
                roi=0.08 + (i * 0.01),  # Improving ROI
                sharpe_ratio=0.7 + (i * 0.1),  # Improving Sharpe
                win_rate=0.55 + (i * 0.02),  # Improving win rate
                max_drawdown=0.12 - (i * 0.01),  # Decreasing drawdown
                expectancy=0.025 + (i * 0.005),
                profit_factor=1.4 + (i * 0.1),
                total_trades=50 + (i * 10),
                avg_trade_duration=45.0,
                volatility=0.15,
                calmar_ratio=0.6,
                sortino_ratio=0.8,
                timestamp=timestamp,
                regime=MarketRegime.TRENDING_BULL
            )
            
            evolution_agent.performance_history[strategy_id].append(metrics)
        
        logger.info(f"Added performance metrics for strategy: {strategy_id}")

async def verify_strategies_in_yaml():
    """Verify that strategies were properly added to the YAML configuration"""
    
    yaml_path = Path("config/options_strategies.yaml")
    if not yaml_path.exists():
        logger.error("❌ YAML configuration file not found!")
        return
    
    with open(yaml_path, 'r') as f:
        config = yaml.safe_load(f)
    
    if 'strategies' not in config:
        logger.error("❌ No strategies section in YAML!")
        return
    
    evolved_strategies = [key for key in config['strategies'].keys() if 'evolved' in key.lower()]
    
    logger.info(f"✅ Found {len(evolved_strategies)} evolved strategies in YAML:")
    for strategy_key in evolved_strategies:
        strategy = config['strategies'][strategy_key]
        logger.info(f"  - {strategy_key}: {strategy.get('name', 'Unknown')}")
        
        # Check if it has performance metadata
        if '_performance_metadata' in strategy:
            metadata = strategy['_performance_metadata']
            logger.info(f"    Performance: ROI={metadata.get('last_roi', 'N/A')}, "
                       f"Sharpe={metadata.get('last_sharpe', 'N/A')}")

async def test_backtesting_integration():
    """Test how the backtesting agent would load and use evolved strategies"""
    
    logger.info("🔍 Testing backtesting agent integration...")
    
    # Simulate how the backtesting agent loads strategies from YAML
    yaml_path = Path("config/options_strategies.yaml")
    if not yaml_path.exists():
        logger.warning("⚠️ YAML file not found for backtesting test")
        return
    
    with open(yaml_path, 'r') as f:
        config = yaml.safe_load(f)
    
    strategies = config.get('strategies', {})
    evolved_strategies = {k: v for k, v in strategies.items() if 'evolved' in k.lower()}
    
    logger.info(f"📊 Backtesting agent would load {len(evolved_strategies)} evolved strategies:")
    
    for strategy_key, strategy_config in evolved_strategies.items():
        logger.info(f"  Strategy: {strategy_key}")
        logger.info(f"    Type: {strategy_config.get('type', 'unknown')}")
        logger.info(f"    Market Outlook: {strategy_config.get('market_outlook', 'unknown')}")
        logger.info(f"    Timeframes: {strategy_config.get('timeframes', [])}")
        logger.info(f"    Entry Conditions: {len(strategy_config.get('entry_conditions', {}))}")
        
        # Check if it has the required fields for backtesting
        required_fields = ['name', 'type', 'parameters', 'risk_management']
        missing_fields = [field for field in required_fields if field not in strategy_config]
        
        if missing_fields:
            logger.warning(f"    ⚠️ Missing fields: {missing_fields}")
        else:
            logger.info(f"    ✅ Ready for backtesting")

if __name__ == "__main__":
    asyncio.run(test_evolution_integration())
