[{"strategy_id": "crossover_2c8e74f9", "name": "Strategy LS_NIFTY_24400_20250718192943", "description": "Crossover of Strategy LS_NIFTY_24400_20250718192943 and Strategy LS_NIFTY_24500_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.01813646760536314, "take_profit": 0.054429026907597544}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_5e7a6b14", "name": "Strategy LS_NIFTY_25900_20250718192943", "description": "Crossover of Strategy LS_NIFTY_25900_20250718192943 and Strategy LP_NIFTY_25300_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.019554440621422968, "take_profit": 0.05164504218350205}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_12298eab", "name": "Strategy LC_NIFTY_24400_20250718192943", "description": "Crossover of Strategy LC_NIFTY_24400_20250718192943 and Strategy LC_NIFTY_24800_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_61e24ba6", "name": "Strategy LP_NIFTY_24400_20250718192943", "description": "Crossover of Strategy LP_NIFTY_24400_20250718192943 and Strategy LS_NIFTY_24700_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_4cca0412", "name": "Strategy LS_NIFTY_24400_20250718192943", "description": "Crossover of Strategy LS_NIFTY_24400_20250718192943 and Strategy LST_NIFTY_25300_24100_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_42100a02", "name": "Strategy LS_NIFTY_24300_20250718192943", "description": "Crossover of Strategy LS_NIFTY_24300_20250718192943 and Strategy LS_NIFTY_24400_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_02492e5f", "name": "Strategy LP_NIFTY_24500_20250718192943", "description": "Crossover of Strategy LP_NIFTY_24500_20250718192943 and Strategy LC_NIFTY_24900_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_3b3358c8", "name": "Strategy ATMLC_NIFTY_25100_20250718192943", "description": "Crossover of Strategy ATMLC_NIFTY_25100_20250718192943 and Strategy LC_NIFTY_25300_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_4c2b0f2b", "name": "Strategy LP_NIFTY_25100_20250718192943", "description": "Crossover of Strategy LP_NIFTY_25100_20250718192943 and Strategy LC_NIFTY_24400_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_a4611fc2", "name": "Strategy LS_NIFTY_25800_20250718192943", "description": "Crossover of Strategy LS_NIFTY_25800_20250718192943 and Strategy LST_NIFTY_25200_24100_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_129c2bc9", "name": "Strategy ATMLC_NIFTY_25000_20250718192943", "description": "Crossover of Strategy ATMLC_NIFTY_25000_20250718192943 and Strategy ATMLP_NIFTY_24900_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.019888610155355743, "take_profit": 0.05041126054587551}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_2bf2eb83", "name": "Strategy LP_NIFTY_25800_20250718192943", "description": "Crossover of Strategy LP_NIFTY_25800_20250718192943 and Strategy LS_NIFTY_25100_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.021236320656243847, "take_profit": 0.04602501829553392}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_2615bcd0", "name": "Strategy ATMLP_NIFTY_24800_20250718192943", "description": "Crossover of Strategy ATMLP_NIFTY_24800_20250718192943 and Strategy LP_NIFTY_25900_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_c2481703", "name": "Strategy LC_NIFTY_24100_20250718192943", "description": "Crossover of Strategy LC_NIFTY_24100_20250718192943 and Strategy LP_NIFTY_24400_20250718192943", "parameters": {}, "entry_conditions": ["delta > 0.3"], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_f64dfcba", "name": "Strategy LP_NIFTY_25900_20250718192943", "description": "Crossover of Strategy LP_NIFTY_25900_20250718192943 and Strategy ATMLC_NIFTY_25100_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_aced454e", "name": "Strategy ATMLC_NIFTY_25100_20250718192943", "description": "Crossover of Strategy ATMLC_NIFTY_25100_20250718192943 and Strategy LP_NIFTY_24400_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_0082fa03", "name": "Strategy ATMLP_NIFTY_25300_20250718192943", "description": "Crossover of Strategy ATMLP_NIFTY_25300_20250718192943 and Strategy LP_NIFTY_24400_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.020916778415223103, "take_profit": 0.04703096262072979}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_11139c3d", "name": "Strategy LST_NIFTY_25100_24100_20250718192943", "description": "Crossover of Strategy LST_NIFTY_25100_24100_20250718192943 and Strategy LP_NIFTY_25700_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.020079077912145124, "take_profit": 0.05858224310146222}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_42afaf66", "name": "Strategy LP_NIFTY_25800_20250718192943", "description": "Crossover of Strategy LP_NIFTY_25800_20250718192943 and Strategy LC_NIFTY_24100_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.024225165105101223, "take_profit": 0.0439540448888829}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_bcc11a45", "name": "Strategy LS_NIFTY_25900_20250718192943", "description": "Crossover of Strategy LS_NIFTY_25900_20250718192943 and Strategy LS_NIFTY_24000_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.019777220310711482, "take_profit": 0.050822521091751026}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_63998b64", "name": "Strategy ATMLP_NIFTY_25000_20250718192943", "description": "Crossover of Strategy ATMLP_NIFTY_25000_20250718192943 and Strategy LS_NIFTY_25600_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_ef5c34ba", "name": "Strategy LP_NIFTY_25100_20250718192943", "description": "Crossover of Strategy LP_NIFTY_25100_20250718192943 and Strategy LC_NIFTY_25300_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02023333431819582, "take_profit": 0.04985178343399133}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_0fb51ce9", "name": "Strategy ATMLC_NIFTY_25000_20250718192943", "description": "Crossover of Strategy ATMLC_NIFTY_25000_20250718192943 and Strategy LP_NIFTY_25900_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.019888610155355743, "take_profit": 0.05041126054587551}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_fdcfa24c", "name": "Strategy ATMLC_NIFTY_24700_20250718192943", "description": "Crossover of Strategy ATMLC_NIFTY_24700_20250718192943 and Strategy ATMLC_NIFTY_25400_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_2a6e2a8b", "name": "Strategy ATMLP_NIFTY_25100_20250718192943", "description": "Crossover of Strategy ATMLP_NIFTY_25100_20250718192943 and Strategy ATMLP_NIFTY_25400_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_66bb5411", "name": "Strategy LST_NIFTY_25300_24000_20250718192943", "description": "Crossover of Strategy LST_NIFTY_25300_24000_20250718192943 and Strategy LS_NIFTY_24400_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.018217441552242666, "take_profit": 0.05221361651476502}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_9164a495", "name": "Strategy LST_NIFTY_25300_24000_20250718192943", "description": "Crossover of Strategy LST_NIFTY_25300_24000_20250718192943 and Strategy LP_NIFTY_24400_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.01834326681210322, "take_profit": 0.04592329872613039}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_0ae56c1b", "name": "Strategy LP_NIFTY_25000_20250718192943", "description": "Crossover of Strategy LP_NIFTY_25000_20250718192943 and Strategy LC_NIFTY_24800_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02070771850226786, "take_profit": 0.0528320693438114}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_006e872c", "name": "Strategy LC_NIFTY_24100_20250718192943", "description": "Crossover of Strategy LC_NIFTY_24100_20250718192943 and Strategy LS_NIFTY_25300_20250718192943", "parameters": {}, "entry_conditions": ["delta > 0.3"], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_c8cda890", "name": "Strategy LS_NIFTY_24700_20250718192943", "description": "Crossover of Strategy LS_NIFTY_24700_20250718192943 and Strategy LC_NIFTY_25100_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.017338631587427385, "take_profit": 0.048577091494376515}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_840af0a9", "name": "Strategy ATMLC_NIFTY_25300_20250718192943", "description": "Crossover of Strategy ATMLC_NIFTY_25300_20250718192943 and Strategy LC_NIFTY_24500_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_ad040559", "name": "Strategy LC_NIFTY_24400_20250718192943", "description": "Crossover of Strategy LC_NIFTY_24400_20250718192943 and Strategy ATMLC_NIFTY_25300_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_ef5c343f", "name": "Strategy LP_NIFTY_24400_20250718192943", "description": "Crossover of Strategy LP_NIFTY_24400_20250718192943 and Strategy LP_NIFTY_25700_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.019309637246229354, "take_profit": 0.04798412014351539}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_5a971cbc", "name": "Strategy LP_NIFTY_25800_20250718192943", "description": "Crossover of Strategy LP_NIFTY_25800_20250718192943 and Strategy LS_NIFTY_25800_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.020618160328121922, "take_profit": 0.04801250914776696}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_5bc83cf5", "name": "Strategy LST_NIFTY_25100_24000_20250718192943", "description": "Crossover of Strategy LST_NIFTY_25100_24000_20250718192943 and Strategy ATMLC_NIFTY_24500_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_a5b14c5b", "name": "Strategy LP_NIFTY_24000_20250718192943", "description": "Crossover of Strategy LP_NIFTY_24000_20250718192943 and Strategy LP_NIFTY_24300_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_4744724f", "name": "Strategy LP_NIFTY_24700_20250718192943", "description": "Crossover of Strategy LP_NIFTY_24700_20250718192943 and Strategy LC_NIFTY_24100_20250718192943", "parameters": {}, "entry_conditions": ["delta > 0.3"], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.01856054116094471, "take_profit": 0.04425787279169696}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_252b2c21", "name": "Strategy LP_NIFTY_25100_20250718192943", "description": "Crossover of Strategy LP_NIFTY_25100_20250718192943 and Strategy LP_NIFTY_24000_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.01560698022053243, "take_profit": 0.04280493056436017}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_8459dea7", "name": "Strategy ATMLP_NIFTY_25300_20250718192943", "description": "Crossover of Strategy ATMLP_NIFTY_25300_20250718192943 and Strategy ATMLP_NIFTY_25300_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.020458389207611553, "take_profit": 0.0485154813103649}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_c7dd083d", "name": "Strategy LP_NIFTY_24000_20250718192943", "description": "Crossover of Strategy LP_NIFTY_24000_20250718192943 and Strategy LS_NIFTY_25000_20250718192943", "parameters": {}, "entry_conditions": ["volume > avg_volume_10 * 1.5"], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_37354a5d", "name": "Strategy ATMLP_NIFTY_25100_20250718192943", "description": "Crossover of Strategy ATMLP_NIFTY_25100_20250718192943 and Strategy LP_NIFTY_24500_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.022085590051652414, "take_profit": 0.05002327308354248}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_40587552", "name": "Strategy LP_NIFTY_24000_20250718192943", "description": "Crossover of Strategy LP_NIFTY_24000_20250718192943 and Strategy LP_NIFTY_24500_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_e820d37b", "name": "Strategy LP_NIFTY_24300_20250718192943", "description": "Crossover of Strategy LP_NIFTY_24300_20250718192943 and Strategy LS_NIFTY_24300_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_f69eaee2", "name": "Strategy ATMLC_NIFTY_25000_20250718192943", "description": "Crossover of Strategy ATMLC_NIFTY_25000_20250718192943 and Strategy ATMLP_NIFTY_24700_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_edd206b0", "name": "Strategy ATMLP_NIFTY_25000_20250718192943", "description": "Crossover of Strategy ATMLP_NIFTY_25000_20250718192943 and Strategy LP_NIFTY_24400_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_86818ecd", "name": "Strategy ATMLC_NIFTY_24500_20250718192943", "description": "Crossover of Strategy ATMLC_NIFTY_24500_20250718192943 and Strategy LS_NIFTY_24600_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_6a817ea9", "name": "Strategy LP_NIFTY_25900_20250718192943", "description": "Crossover of Strategy LP_NIFTY_25900_20250718192943 and Strategy LST_NIFTY_25300_24000_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.019108720776121333, "take_profit": 0.05110680825738251}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_03e1a38f", "name": "Strategy ATMLP_NIFTY_24500_20250718192943", "description": "Crossover of Strategy ATMLP_NIFTY_24500_20250718192943 and Strategy LS_NIFTY_24000_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_0923b06f", "name": "Strategy LST_NIFTY_25300_24200_20250718192943", "description": "Crossover of Strategy LST_NIFTY_25300_24200_20250718192943 and Strategy ATMLC_NIFTY_25000_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_e07eb079", "name": "Strategy LP_NIFTY_24500_20250718192943", "description": "Crossover of Strategy LP_NIFTY_24500_20250718192943 and Strategy LC_NIFTY_24600_20250718192943", "parameters": {}, "entry_conditions": ["price_change_1d < 0.02"], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.019842526316495565, "take_profit": 0.049850732783729076}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_d22d4cdd", "name": "Strategy LP_NIFTY_25900_20250718192943", "description": "Crossover of Strategy LP_NIFTY_25900_20250718192943 and Strategy LP_NIFTY_24000_20250718192943", "parameters": {}, "entry_conditions": ["volume > avg_volume_10 * 1.5", "price_change_1d < 0.02"], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.019882570179349107, "take_profit": 0.048442310663173366}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_db736e6d", "name": "Strategy LP_NIFTY_24000_20250718192943", "description": "Crossover of Strategy LP_NIFTY_24000_20250718192943 and Strategy LS_NIFTY_25500_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_d67eec97", "name": "Strategy ATMLP_NIFTY_24700_20250718192943", "description": "Crossover of Strategy ATMLP_NIFTY_24700_20250718192943 and Strategy LS_NIFTY_25200_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.01882144547333959, "take_profit": 0.04889139975258801}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_fd6efe84", "name": "Strategy LC_NIFTY_25100_20250718192943", "description": "Crossover of Strategy LC_NIFTY_25100_20250718192943 and Strategy LST_NIFTY_25200_24100_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_8aa007aa", "name": "Strategy LP_NIFTY_25900_20250718192943", "description": "Crossover of Strategy LP_NIFTY_25900_20250718192943 and Strategy ATMLC_NIFTY_25000_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_45f630b8", "name": "Strategy ATMLC_NIFTY_24500_20250718192943", "description": "Crossover of Strategy ATMLC_NIFTY_24500_20250718192943 and Strategy LS_NIFTY_25200_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_c27d2156", "name": "Strategy ATMLP_NIFTY_25300_20250718192943", "description": "Crossover of Strategy ATMLP_NIFTY_25300_20250718192943 and Strategy ATMLC_NIFTY_25000_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02017349968148365, "take_profit": 0.049463370928120205}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_203983e5", "name": "Strategy ATMLC_NIFTY_24500_20250718192943", "description": "Crossover of Strategy ATMLC_NIFTY_24500_20250718192943 and Strategy LP_NIFTY_25800_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02030908016406096, "take_profit": 0.04900625457388348}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_d2d6be1c", "name": "Strategy LS_NIFTY_25200_20250718192943", "description": "Crossover of Strategy LS_NIFTY_25200_20250718192943 and Strategy ATMLC_NIFTY_24500_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.017088067557768552, "take_profit": 0.06003988592356354}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_5994bbb2", "name": "Strategy LP_NIFTY_24500_20250718192943", "description": "Crossover of Strategy LP_NIFTY_24500_20250718192943 and Strategy LP_NIFTY_24200_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_c4cd7f56", "name": "Strategy ATMLP_NIFTY_24600_20250718192943", "description": "Crossover of Strategy ATMLP_NIFTY_24600_20250718192943 and Strategy LP_NIFTY_24900_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_c395d132", "name": "Strategy ATMLC_NIFTY_25000_20250718192943", "description": "Crossover of Strategy ATMLC_NIFTY_25000_20250718192943 and Strategy ATMLC_NIFTY_24500_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_4ec308c0", "name": "Strategy ATMLP_NIFTY_25300_20250718192943", "description": "Crossover of Strategy ATMLP_NIFTY_25300_20250718192943 and Strategy LP_NIFTY_24000_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.020458389207611553, "take_profit": 0.0485154813103649}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}]