[{"strategy_id": "crossover_58a0eaac", "name": "Strategy ATMLC_NIFTY_25000_20250718192943", "description": "Crossover of Strategy ATMLC_NIFTY_25000_20250718192943 and Strategy LP_NIFTY_24700_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_44681992", "name": "Strategy LST_NIFTY_25300_24200_20250718192943", "description": "Crossover of Strategy LST_NIFTY_25300_24200_20250718192943 and Strategy ATMLP_NIFTY_25400_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.01910415200807508, "take_profit": 0.050980558963467676}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_b1d3ae99", "name": "Strategy LC_NIFTY_25200_20250718192943", "description": "Crossover of Strategy LC_NIFTY_25200_20250718192943 and Strategy ATMLP_NIFTY_24500_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_0543d9b7", "name": "Strategy LC_NIFTY_25000_20250718192943", "description": "Crossover of Strategy LC_NIFTY_25000_20250718192943 and Strategy LC_NIFTY_24600_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.01891308118943477, "take_profit": 0.04931181173088793}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_d6d8f515", "name": "Strategy LC_NIFTY_25500_20250718192943", "description": "Crossover of Strategy LC_NIFTY_25500_20250718192943 and Strategy ATMLP_NIFTY_25200_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.022009190221835513, "take_profit": 0.05472659276455519}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_f48a15b0", "name": "Strategy LST_NIFTY_25200_24000_20250718192943", "description": "Crossover of Strategy LST_NIFTY_25200_24000_20250718192943 and Strategy LP_NIFTY_25900_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_81734934", "name": "Strategy LP_NIFTY_24500_20250718192943", "description": "Crossover of Strategy LP_NIFTY_24500_20250718192943 and Strategy ATMLC_NIFTY_25200_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_cc040fb5", "name": "Strategy ATMLC_NIFTY_24500_20250718192943", "description": "Crossover of Strategy ATMLC_NIFTY_24500_20250718192943 and Strategy LS_NIFTY_24200_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_4bd47554", "name": "Strategy LP_NIFTY_24500_20250718192943", "description": "Crossover of Strategy LP_NIFTY_24500_20250718192943 and Strategy LP_NIFTY_24500_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02103396966040894, "take_profit": 0.04978405309914204}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_5cc97209", "name": "Strategy ATMLC_NIFTY_25300_20250718192943", "description": "Crossover of Strategy ATMLC_NIFTY_25300_20250718192943 and Strategy ATMLC_NIFTY_25100_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_4f580ceb", "name": "Strategy ATMLC_NIFTY_25300_20250718192943", "description": "Crossover of Strategy ATMLC_NIFTY_25300_20250718192943 and Strategy LS_NIFTY_25800_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_670a597b", "name": "Strategy LC_NIFTY_24500_20250718192943", "description": "Crossover of Strategy LC_NIFTY_24500_20250718192943 and Strategy LP_NIFTY_24500_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_eb53a668", "name": "Strategy ATMLC_NIFTY_25300_20250718192943", "description": "Crossover of Strategy ATMLC_NIFTY_25300_20250718192943 and Strategy LST_NIFTY_25100_24200_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.019430944277680506, "take_profit": 0.04959127002857267}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_bd14c5c3", "name": "Strategy LST_NIFTY_25100_24100_20250718192943", "description": "Crossover of Strategy LST_NIFTY_25100_24100_20250718192943 and Strategy LST_NIFTY_25300_24200_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_36544fbc", "name": "Strategy LS_NIFTY_25200_20250718192943", "description": "Crossover of Strategy LS_NIFTY_25200_20250718192943 and Strategy LS_NIFTY_24300_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_99127475", "name": "Strategy ATMLP_NIFTY_25400_20250718192943", "description": "Crossover of Strategy ATMLP_NIFTY_25400_20250718192943 and Strategy LC_NIFTY_24500_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_1857d3a7", "name": "Strategy LST_NIFTY_25300_24200_20250718192943", "description": "Crossover of Strategy LST_NIFTY_25300_24200_20250718192943 and Strategy LP_NIFTY_25900_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.019552076004037543, "take_profit": 0.05049027948173384}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_f8bed665", "name": "Strategy ATMLC_NIFTY_25300_20250718192943", "description": "Crossover of Strategy ATMLC_NIFTY_25300_20250718192943 and Strategy ATMLC_NIFTY_24500_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.019715472138840253, "take_profit": 0.04979563501428634}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_80cb33a4", "name": "Strategy LST_NIFTY_25100_24100_20250718192943", "description": "Crossover of Strategy LST_NIFTY_25100_24100_20250718192943 and Strategy ATMLP_NIFTY_24800_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.018905887388000874, "take_profit": 0.04188487774824593}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_cb6a4148", "name": "Strategy LP_NIFTY_24800_20250718192943", "description": "Crossover of Strategy LP_NIFTY_24800_20250718192943 and Strategy LS_NIFTY_25800_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_ddad830c", "name": "Strategy LP_NIFTY_25700_20250718192943", "description": "Crossover of Strategy LP_NIFTY_25700_20250718192943 and Strategy ATMLP_NIFTY_24900_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_b3183894", "name": "Strategy ATMLC_NIFTY_25500_20250718192943", "description": "Crossover of Strategy ATMLC_NIFTY_25500_20250718192943 and Strategy LP_NIFTY_25900_20250718192943", "parameters": {}, "entry_conditions": ["vix < 25"], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.01817572318546483, "take_profit": 0.056024775018210175}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_d5905bf8", "name": "Strategy ATMLC_NIFTY_25300_20250718192943", "description": "Crossover of Strategy ATMLC_NIFTY_25300_20250718192943 and Strategy LC_NIFTY_25200_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.019857736069420127, "take_profit": 0.04989781750714317}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_946857eb", "name": "Strategy LP_NIFTY_24400_20250718192943", "description": "Crossover of Strategy LP_NIFTY_24400_20250718192943 and Strategy LS_NIFTY_25400_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_f80f0d84", "name": "Strategy ATMLC_NIFTY_24500_20250718192943", "description": "Crossover of Strategy ATMLC_NIFTY_24500_20250718192943 and Strategy ATMLC_NIFTY_25000_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_ccf99baf", "name": "Strategy LC_NIFTY_25800_20250718192943", "description": "Crossover of Strategy LC_NIFTY_25800_20250718192943 and Strategy ATMLC_NIFTY_24500_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_a649d1f8", "name": "Strategy LS_NIFTY_25400_20250718192943", "description": "Crossover of Strategy LS_NIFTY_25400_20250718192943 and Strategy ATMLC_NIFTY_25500_20250718192943", "parameters": {}, "entry_conditions": ["vix < 25"], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.019087861592732416, "take_profit": 0.053012387509105086}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_117ed95d", "name": "Strategy LS_NIFTY_25200_20250718192943", "description": "Crossover of Strategy LS_NIFTY_25200_20250718192943 and Strategy ATMLP_NIFTY_25200_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_d2b8425d", "name": "Strategy LC_NIFTY_25700_20250718192943", "description": "Crossover of Strategy LC_NIFTY_25700_20250718192943 and Strategy ATMLP_NIFTY_25400_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_94b1a955", "name": "Strategy LP_NIFTY_25700_20250718192943", "description": "Crossover of Strategy LP_NIFTY_25700_20250718192943 and Strategy LC_NIFTY_25700_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_5c75d05b", "name": "Strategy ATMLP_NIFTY_25400_20250718192943", "description": "Crossover of Strategy ATMLP_NIFTY_25400_20250718192943 and Strategy LC_NIFTY_24600_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.021550686943101246, "take_profit": 0.05381685544339918}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_6c9a7c0a", "name": "Strategy LP_NIFTY_24300_20250718192943", "description": "Crossover of Strategy LP_NIFTY_24300_20250718192943 and Strategy ATMLP_NIFTY_25400_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_2d7d75a3", "name": "Strategy LST_NIFTY_25100_24000_20250718192943", "description": "Crossover of Strategy LST_NIFTY_25100_24000_20250718192943 and Strategy LP_NIFTY_25700_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_8ac532c9", "name": "Strategy LST_NIFTY_25100_24200_20250718192943", "description": "Crossover of Strategy LST_NIFTY_25100_24200_20250718192943 and Strategy LST_NIFTY_25200_24000_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_52dc986d", "name": "Strategy LC_NIFTY_25800_20250718192943", "description": "Crossover of Strategy LC_NIFTY_25800_20250718192943 and Strategy LP_NIFTY_24000_20250718192943", "parameters": {}, "entry_conditions": ["time_to_expiry > 7"], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_398f2a9a", "name": "Strategy LS_NIFTY_25200_20250718192943", "description": "Crossover of Strategy LS_NIFTY_25200_20250718192943 and Strategy LC_NIFTY_25500_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_b96d2b4c", "name": "Strategy LST_NIFTY_25200_24000_20250718192943", "description": "Crossover of Strategy LST_NIFTY_25200_24000_20250718192943 and Strategy ATMLC_NIFTY_24500_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_bffa6a7a", "name": "Strategy LC_NIFTY_25200_20250718192943", "description": "Crossover of Strategy LC_NIFTY_25200_20250718192943 and Strategy LC_NIFTY_24800_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_610d4ead", "name": "Strategy ATMLC_NIFTY_25500_20250718192943", "description": "Crossover of Strategy ATMLC_NIFTY_25500_20250718192943 and Strategy LP_NIFTY_24000_20250718192943", "parameters": {}, "entry_conditions": ["vix < 25"], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.019347494604385798, "take_profit": 0.04331141710971916}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_1cd6b7ac", "name": "Strategy LST_NIFTY_25100_24100_20250718192943", "description": "Crossover of Strategy LST_NIFTY_25100_24100_20250718192943 and Strategy LC_NIFTY_25600_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.019452943694000437, "take_profit": 0.04594243887412297}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_222935c9", "name": "Strategy ATMLC_NIFTY_25300_20250718192943", "description": "Crossover of Strategy ATMLC_NIFTY_25300_20250718192943 and Strategy ATMLC_NIFTY_25500_20250718192943", "parameters": {}, "entry_conditions": ["vix < 25"], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.01956789173287457, "take_profit": 0.0521584116852597}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_7d366de1", "name": "Strategy LC_NIFTY_25300_20250718192943", "description": "Crossover of Strategy LC_NIFTY_25300_20250718192943 and Strategy LC_NIFTY_25800_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}]