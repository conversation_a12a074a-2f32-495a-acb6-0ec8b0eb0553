[{"strategy_id": "crossover_7345e80f", "name": "Strategy LC_NIFTY_24300_20250718192943", "description": "Crossover of Strategy LC_NIFTY_24300_20250718192943 and Strategy LS_NIFTY_24600_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02222475872652967, "take_profit": 0.05317340944194015}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_3b8bd77a", "name": "Strategy LS_NIFTY_25400_20250718192943", "description": "Crossover of Strategy LS_NIFTY_25400_20250718192943 and Strategy LST_NIFTY_25200_24200_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_ba59a6de", "name": "Strategy LC_NIFTY_24300_20250718192943", "description": "Crossover of Strategy LC_NIFTY_24300_20250718192943 and Strategy LS_NIFTY_25500_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_2bcc01da", "name": "Strategy LS_NIFTY_24600_20250718192943", "description": "Crossover of Strategy LS_NIFTY_24600_20250718192943 and Strategy LP_NIFTY_24900_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.020857122630091138, "take_profit": 0.05018189786514318}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_b195aa31", "name": "Strategy LP_NIFTY_24400_20250718192943", "description": "Crossover of Strategy LP_NIFTY_24400_20250718192943 and Strategy ATMLC_NIFTY_25100_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_e7c83e84", "name": "Strategy LS_NIFTY_24500_20250718192943", "description": "Crossover of Strategy LS_NIFTY_24500_20250718192943 and Strategy ATMLP_NIFTY_25500_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_94dd3375", "name": "Strategy LC_NIFTY_24500_20250718192943", "description": "Crossover of Strategy LC_NIFTY_24500_20250718192943 and Strategy LST_NIFTY_25200_24100_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}]