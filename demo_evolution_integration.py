#!/usr/bin/env python3
"""
Demonstration script showing how the evolution agent integrates with the trading system.
This script shows the complete workflow from strategy evolution to production use.
"""

import asyncio
import yaml
import json
import logging
from pathlib import Path
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def demonstrate_integration_workflow():
    """Demonstrate the complete integration workflow"""
    
    print("🎯 Options Strategy Evolution Agent Integration Demo")
    print("=" * 60)
    
    print("\n📋 OVERVIEW:")
    print("The evolution agent now properly integrates with the trading system by:")
    print("1. Evolving strategies based on performance feedback")
    print("2. Exporting successful strategies to config/options_strategies.yaml")
    print("3. Making evolved strategies available to backtesting and signal generation")
    print("4. Managing strategy lifecycle (promotion, demotion, retirement)")
    
    print("\n🔄 INTEGRATION FLOW:")
    print("┌─────────────────────────────────────────────────────────────┐")
    print("│ 1. Evolution Agent runs continuously (every 30 minutes)    │")
    print("│    - Monitors strategy performance                          │")
    print("│    - Creates mutations of underperforming strategies       │")
    print("│    - Evaluates new strategy variants                       │")
    print("└─────────────────────────────────────────────────────────────┘")
    print("                              │")
    print("                              ▼")
    print("┌─────────────────────────────────────────────────────────────┐")
    print("│ 2. Performance Evaluation (every 5 minutes)                │")
    print("│    - Tracks ROI, Sharpe ratio, win rate, drawdown          │")
    print("│    - Identifies strategies ready for production             │")
    print("│    - Flags underperforming strategies for evolution        │")
    print("└─────────────────────────────────────────────────────────────┘")
    print("                              │")
    print("                              ▼")
    print("┌─────────────────────────────────────────────────────────────┐")
    print("│ 3. Production Sync (every 2 hours)                         │")
    print("│    - Exports successful strategies to YAML config          │")
    print("│    - Removes deprecated strategies from production         │")
    print("│    - Creates backup of original configuration              │")
    print("└─────────────────────────────────────────────────────────────┘")
    print("                              │")
    print("                              ▼")
    print("┌─────────────────────────────────────────────────────────────┐")
    print("│ 4. Backtesting & Signal Generation Use Evolved Strategies  │")
    print("│    - Backtesting agent loads from config/options_strategies.yaml │")
    print("│    - Signal generation agent uses evolved strategies       │")
    print("│    - Trading models benefit from optimized parameters      │")
    print("└─────────────────────────────────────────────────────────────┘")
    
    print("\n📊 STRATEGY LIFECYCLE MANAGEMENT:")
    print("• EXPERIMENTAL → Performance testing phase")
    print("• ACTIVE → Meets basic performance criteria")
    print("• PROMOTED → Exported to production configuration")
    print("• DEMOTED → Removed from production due to poor performance")
    print("• DEPRECATED → Archived and no longer used")
    
    print("\n🎯 PRODUCTION CRITERIA:")
    print("For a strategy to be promoted to production, it must have:")
    print("• ROI ≥ 7.5% (50% higher than minimum)")
    print("• Sharpe Ratio ≥ 0.6 (20% higher than minimum)")
    print("• Win Rate ≥ 49.5% (10% higher than minimum)")
    print("• Max Drawdown ≤ 12% (20% lower than maximum)")
    print("• At least 3 performance measurements")
    
    print("\n🔧 CONFIGURATION FILES:")
    print("• Evolution strategies: data/strategy_evolution/registry/strategy_registry.json")
    print("• Production strategies: config/options_strategies.yaml")
    print("• Performance history: data/strategy_evolution/performance_history.parquet")
    print("• Evolution logs: data/strategy_evolution/logs/")
    
    print("\n📈 EXAMPLE EVOLVED STRATEGY IN YAML:")
    show_example_yaml_strategy()
    
    print("\n🚀 BENEFITS:")
    print("✅ Continuous strategy optimization")
    print("✅ Automatic parameter tuning")
    print("✅ Performance-based strategy selection")
    print("✅ Seamless integration with existing agents")
    print("✅ Backup and rollback capabilities")
    print("✅ Comprehensive performance tracking")
    
    print("\n🔍 MONITORING:")
    print("• Check logs for evolution events")
    print("• Monitor config/options_strategies.yaml for new strategies")
    print("• Review performance metrics in evolution history")
    print("• Watch for promotion/demotion notifications")
    
    print("\n💡 USAGE:")
    print("The evolution agent runs automatically when started.")
    print("You can also manually trigger exports using the public API:")
    print("  await evolution_agent.export_strategies_to_production()")
    print("  await evolution_agent.promote_strategy_to_production(strategy_id)")
    print("  await evolution_agent.get_evolved_strategies_summary()")

def show_example_yaml_strategy():
    """Show an example of how an evolved strategy appears in the YAML file"""
    
    example_strategy = {
        'evolved_momentum_call_v1': {
            'name': 'Evolved Momentum Call',
            'type': 'directional',
            'description': 'Evolved strategy from momentum_long_call',
            'market_outlook': 'bullish',
            'volatility_outlook': 'moderate',
            'timeframes': ['5min'],
            'underlyings': ['NIFTY', 'BANKNIFTY'],
            'parameters': {
                'entry_conditions': ['rsi_14 > 52', 'underlying_above_ema_18'],
                'confidence_threshold': 0.42,
                'max_signals_per_day': 6
            },
            'entry_conditions': {
                'rsi_14': '> 52',
                'underlying_price': '> ema_18'
            },
            'exit_conditions': {
                'profit_target': 0.45,
                'stop_loss': 0.25,
                'time_exit': '15:15'
            },
            'risk_management': {
                'max_loss_per_trade': 800,
                'position_size_pct': 0.025,
                'max_concurrent_positions': 2
            },
            '_performance_metadata': {
                'last_roi': 0.0856,
                'last_sharpe': 0.82,
                'last_win_rate': 0.587,
                'last_updated': '2024-08-16T10:30:00',
                'evolution_version': 'v1.2',
                'parent_strategy': 'momentum_long_call'
            }
        }
    }
    
    print(yaml.dump(example_strategy, default_flow_style=False, indent=2))

def check_current_integration_status():
    """Check the current status of the integration"""
    
    print("\n🔍 CURRENT INTEGRATION STATUS:")
    print("-" * 40)
    
    # Check if evolution agent files exist
    evolution_files = [
        "agents/options_strategy_evolution_agent.py",
        "config/options_strategies.yaml"
    ]
    
    for file_path in evolution_files:
        if Path(file_path).exists():
            print(f"✅ {file_path} - EXISTS")
        else:
            print(f"❌ {file_path} - MISSING")
    
    # Check if YAML has evolved strategies
    yaml_path = Path("config/options_strategies.yaml")
    if yaml_path.exists():
        try:
            with open(yaml_path, 'r') as f:
                config = yaml.safe_load(f)
            
            strategies = config.get('strategies', {})
            evolved_count = len([k for k in strategies.keys() if 'evolved' in k.lower()])
            
            print(f"📊 Total strategies in YAML: {len(strategies)}")
            print(f"🧬 Evolved strategies in YAML: {evolved_count}")
            
            if evolved_count > 0:
                print("✅ Integration is working - evolved strategies found in production config")
            else:
                print("⚠️ No evolved strategies found in production config yet")
                
        except Exception as e:
            print(f"❌ Error reading YAML config: {e}")
    
    # Check evolution data directories
    evolution_dirs = [
        "data/strategy_evolution",
        "data/strategy_evolution/registry",
        "data/strategy_evolution/logs"
    ]
    
    for dir_path in evolution_dirs:
        if Path(dir_path).exists():
            print(f"✅ {dir_path} - EXISTS")
        else:
            print(f"⚠️ {dir_path} - WILL BE CREATED ON FIRST RUN")

if __name__ == "__main__":
    demonstrate_integration_workflow()
    check_current_integration_status()
    
    print("\n" + "=" * 60)
    print("🎉 Evolution Agent Integration is Ready!")
    print("Run the evolution agent to start the continuous optimization process.")
    print("=" * 60)
