[{"strategy_id": "crossover_8745f18f", "name": "Strategy LS_NIFTY_25700_20250718192943", "description": "Crossover of Strategy LS_NIFTY_25700_20250718192943 and Strategy ATMLC_NIFTY_24500_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_e9dc39ee", "name": "Strategy LP_NIFTY_25600_20250718192943", "description": "Crossover of Strategy LP_NIFTY_25600_20250718192943 and Strategy LS_NIFTY_25700_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_bce52921", "name": "Strategy LP_NIFTY_24100_20250718192943", "description": "Crossover of Strategy LP_NIFTY_24100_20250718192943 and Strategy LC_NIFTY_24700_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_bfc0388b", "name": "Strategy LS_NIFTY_24800_20250718192943", "description": "Crossover of Strategy LS_NIFTY_24800_20250718192943 and Strategy LC_NIFTY_25000_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_e0b9082a", "name": "Strategy LP_NIFTY_24400_20250718192943", "description": "Crossover of Strategy LP_NIFTY_24400_20250718192943 and Strategy LC_NIFTY_24200_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_faa546dc", "name": "Strategy ATMLC_NIFTY_25500_20250718192943", "description": "Crossover of Strategy ATMLC_NIFTY_25500_20250718192943 and Strategy LC_NIFTY_25000_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}]