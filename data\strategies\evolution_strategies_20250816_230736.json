[{"strategy_id": "crossover_70f00d35", "name": "Strategy LC_NIFTY_25300_20250718192943", "description": "Crossover of Strategy LC_NIFTY_25300_20250718192943 and Strategy LS_NIFTY_25500_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_e2496fe6", "name": "Strategy ATMLP_NIFTY_24600_20250718192943", "description": "Crossover of Strategy ATMLP_NIFTY_24600_20250718192943 and Strategy LS_NIFTY_24900_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_eed8e144", "name": "Strategy LC_NIFTY_25300_20250718192943", "description": "Crossover of Strategy LC_NIFTY_25300_20250718192943 and Strategy LS_NIFTY_24600_20250718192943", "parameters": {}, "entry_conditions": ["delta < 0.7"], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.01981518735318518, "take_profit": 0.04824248554480734}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}]