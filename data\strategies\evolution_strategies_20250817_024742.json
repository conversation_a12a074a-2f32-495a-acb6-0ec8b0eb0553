[{"strategy_id": "crossover_fe7db791", "name": "Strategy LP_NIFTY_24100_20250718192943", "description": "Crossover of Strategy LP_NIFTY_24100_20250718192943 and Strategy LC_NIFTY_24200_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_b5b3d448", "name": "Strategy LP_NIFTY_25000_20250718192943", "description": "Crossover of Strategy LP_NIFTY_25000_20250718192943 and Strategy ATMLP_NIFTY_25400_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.01941303084307291, "take_profit": 0.05156153213064976}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_e0af3c4a", "name": "Strategy LS_NIFTY_24400_20250718192943", "description": "Crossover of Strategy LS_NIFTY_24400_20250718192943 and Strategy LS_NIFTY_25700_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_db1380f8", "name": "Strategy LS_NIFTY_25500_20250718192943", "description": "Crossover of Strategy LS_NIFTY_25500_20250718192943 and Strategy LP_NIFTY_24600_20250718192943", "parameters": {}, "entry_conditions": ["time_to_expiry < 45"], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.021484133606811787, "take_profit": 0.04267673856246759}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_30b31f9d", "name": "Strategy LST_NIFTY_25300_24200_20250718192943", "description": "Crossover of Strategy LST_NIFTY_25300_24200_20250718192943 and Strategy LP_NIFTY_24500_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_1e680fc3", "name": "Strategy LS_NIFTY_24900_20250718192943", "description": "Crossover of Strategy LS_NIFTY_24900_20250718192943 and Strategy LP_NIFTY_25000_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.019706515421536456, "take_profit": 0.05078076606532488}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}]