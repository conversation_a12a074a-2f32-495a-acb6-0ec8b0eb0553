[{"strategy_id": "crossover_2c8e74f9", "name": "Strategy LS_NIFTY_24400_20250718192943", "description": "Crossover of Strategy LS_NIFTY_24400_20250718192943 and Strategy LS_NIFTY_24500_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.01813646760536314, "take_profit": 0.054429026907597544}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_5e7a6b14", "name": "Strategy LS_NIFTY_25900_20250718192943", "description": "Crossover of Strategy LS_NIFTY_25900_20250718192943 and Strategy LP_NIFTY_25300_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.019554440621422968, "take_profit": 0.05164504218350205}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_12298eab", "name": "Strategy LC_NIFTY_24400_20250718192943", "description": "Crossover of Strategy LC_NIFTY_24400_20250718192943 and Strategy LC_NIFTY_24800_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_61e24ba6", "name": "Strategy LP_NIFTY_24400_20250718192943", "description": "Crossover of Strategy LP_NIFTY_24400_20250718192943 and Strategy LS_NIFTY_24700_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_42100a02", "name": "Strategy LS_NIFTY_24300_20250718192943", "description": "Crossover of Strategy LS_NIFTY_24300_20250718192943 and Strategy LS_NIFTY_24400_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_a4611fc2", "name": "Strategy LS_NIFTY_25800_20250718192943", "description": "Crossover of Strategy LS_NIFTY_25800_20250718192943 and Strategy LST_NIFTY_25200_24100_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_129c2bc9", "name": "Strategy ATMLC_NIFTY_25000_20250718192943", "description": "Crossover of Strategy ATMLC_NIFTY_25000_20250718192943 and Strategy ATMLP_NIFTY_24900_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.019888610155355743, "take_profit": 0.05041126054587551}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_2bf2eb83", "name": "Strategy LP_NIFTY_25800_20250718192943", "description": "Crossover of Strategy LP_NIFTY_25800_20250718192943 and Strategy LS_NIFTY_25100_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.021236320656243847, "take_profit": 0.04602501829553392}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_2615bcd0", "name": "Strategy ATMLP_NIFTY_24800_20250718192943", "description": "Crossover of Strategy ATMLP_NIFTY_24800_20250718192943 and Strategy LP_NIFTY_25900_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_c2481703", "name": "Strategy LC_NIFTY_24100_20250718192943", "description": "Crossover of Strategy LC_NIFTY_24100_20250718192943 and Strategy LP_NIFTY_24400_20250718192943", "parameters": {}, "entry_conditions": ["delta > 0.3"], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_aced454e", "name": "Strategy ATMLC_NIFTY_25100_20250718192943", "description": "Crossover of Strategy ATMLC_NIFTY_25100_20250718192943 and Strategy LP_NIFTY_24400_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_42afaf66", "name": "Strategy LP_NIFTY_25800_20250718192943", "description": "Crossover of Strategy LP_NIFTY_25800_20250718192943 and Strategy LC_NIFTY_24100_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.024225165105101223, "take_profit": 0.0439540448888829}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_bcc11a45", "name": "Strategy LS_NIFTY_25900_20250718192943", "description": "Crossover of Strategy LS_NIFTY_25900_20250718192943 and Strategy LS_NIFTY_24000_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.019777220310711482, "take_profit": 0.050822521091751026}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_63998b64", "name": "Strategy ATMLP_NIFTY_25000_20250718192943", "description": "Crossover of Strategy ATMLP_NIFTY_25000_20250718192943 and Strategy LS_NIFTY_25600_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_ef5c34ba", "name": "Strategy LP_NIFTY_25100_20250718192943", "description": "Crossover of Strategy LP_NIFTY_25100_20250718192943 and Strategy LC_NIFTY_25300_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02023333431819582, "take_profit": 0.04985178343399133}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_2a6e2a8b", "name": "Strategy ATMLP_NIFTY_25100_20250718192943", "description": "Crossover of Strategy ATMLP_NIFTY_25100_20250718192943 and Strategy ATMLP_NIFTY_25400_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_0ae56c1b", "name": "Strategy LP_NIFTY_25000_20250718192943", "description": "Crossover of Strategy LP_NIFTY_25000_20250718192943 and Strategy LC_NIFTY_24800_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02070771850226786, "take_profit": 0.0528320693438114}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_c8cda890", "name": "Strategy LS_NIFTY_24700_20250718192943", "description": "Crossover of Strategy LS_NIFTY_24700_20250718192943 and Strategy LC_NIFTY_25100_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.017338631587427385, "take_profit": 0.048577091494376515}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_840af0a9", "name": "Strategy ATMLC_NIFTY_25300_20250718192943", "description": "Crossover of Strategy ATMLC_NIFTY_25300_20250718192943 and Strategy LC_NIFTY_24500_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_ef5c343f", "name": "Strategy LP_NIFTY_24400_20250718192943", "description": "Crossover of Strategy LP_NIFTY_24400_20250718192943 and Strategy LP_NIFTY_25700_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.019309637246229354, "take_profit": 0.04798412014351539}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_5a971cbc", "name": "Strategy LP_NIFTY_25800_20250718192943", "description": "Crossover of Strategy LP_NIFTY_25800_20250718192943 and Strategy LS_NIFTY_25800_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.020618160328121922, "take_profit": 0.04801250914776696}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_a5b14c5b", "name": "Strategy LP_NIFTY_24000_20250718192943", "description": "Crossover of Strategy LP_NIFTY_24000_20250718192943 and Strategy LP_NIFTY_24300_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_4744724f", "name": "Strategy LP_NIFTY_24700_20250718192943", "description": "Crossover of Strategy LP_NIFTY_24700_20250718192943 and Strategy LC_NIFTY_24100_20250718192943", "parameters": {}, "entry_conditions": ["delta > 0.3"], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.01856054116094471, "take_profit": 0.04425787279169696}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_c7dd083d", "name": "Strategy LP_NIFTY_24000_20250718192943", "description": "Crossover of Strategy LP_NIFTY_24000_20250718192943 and Strategy LS_NIFTY_25000_20250718192943", "parameters": {}, "entry_conditions": ["volume > avg_volume_10 * 1.5"], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_e820d37b", "name": "Strategy LP_NIFTY_24300_20250718192943", "description": "Crossover of Strategy LP_NIFTY_24300_20250718192943 and Strategy LS_NIFTY_24300_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_edd206b0", "name": "Strategy ATMLP_NIFTY_25000_20250718192943", "description": "Crossover of Strategy ATMLP_NIFTY_25000_20250718192943 and Strategy LP_NIFTY_24400_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_6a817ea9", "name": "Strategy LP_NIFTY_25900_20250718192943", "description": "Crossover of Strategy LP_NIFTY_25900_20250718192943 and Strategy LST_NIFTY_25300_24000_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.019108720776121333, "take_profit": 0.05110680825738251}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_0923b06f", "name": "Strategy LST_NIFTY_25300_24200_20250718192943", "description": "Crossover of Strategy LST_NIFTY_25300_24200_20250718192943 and Strategy ATMLC_NIFTY_25000_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_e07eb079", "name": "Strategy LP_NIFTY_24500_20250718192943", "description": "Crossover of Strategy LP_NIFTY_24500_20250718192943 and Strategy LC_NIFTY_24600_20250718192943", "parameters": {}, "entry_conditions": ["price_change_1d < 0.02"], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.019842526316495565, "take_profit": 0.049850732783729076}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_d22d4cdd", "name": "Strategy LP_NIFTY_25900_20250718192943", "description": "Crossover of Strategy LP_NIFTY_25900_20250718192943 and Strategy LP_NIFTY_24000_20250718192943", "parameters": {}, "entry_conditions": ["volume > avg_volume_10 * 1.5", "price_change_1d < 0.02"], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.019882570179349107, "take_profit": 0.048442310663173366}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_db736e6d", "name": "Strategy LP_NIFTY_24000_20250718192943", "description": "Crossover of Strategy LP_NIFTY_24000_20250718192943 and Strategy LS_NIFTY_25500_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_c27d2156", "name": "Strategy ATMLP_NIFTY_25300_20250718192943", "description": "Crossover of Strategy ATMLP_NIFTY_25300_20250718192943 and Strategy ATMLC_NIFTY_25000_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02017349968148365, "take_profit": 0.049463370928120205}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_c4cd7f56", "name": "Strategy ATMLP_NIFTY_24600_20250718192943", "description": "Crossover of Strategy ATMLP_NIFTY_24600_20250718192943 and Strategy LP_NIFTY_24900_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_4ec308c0", "name": "Strategy ATMLP_NIFTY_25300_20250718192943", "description": "Crossover of Strategy ATMLP_NIFTY_25300_20250718192943 and Strategy LP_NIFTY_24000_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.020458389207611553, "take_profit": 0.0485154813103649}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_b893f2c6", "name": "Strategy LC_NIFTY_24300_20250718192943", "description": "Crossover of Strategy LC_NIFTY_24300_20250718192943 and Strategy ATMLP_NIFTY_25300_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_cab8da98", "name": "Strategy ATMLP_NIFTY_24800_20250718192943", "description": "Crossover of Strategy ATMLP_NIFTY_24800_20250718192943 and Strategy LST_NIFTY_25300_24100_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_b3041b69", "name": "Strategy ATMLP_NIFTY_25200_20250718192943", "description": "Crossover of Strategy ATMLP_NIFTY_25200_20250718192943 and Strategy LP_NIFTY_25400_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_5c2b30ac", "name": "Strategy LC_NIFTY_25200_20250718192943", "description": "Crossover of Strategy LC_NIFTY_25200_20250718192943 and Strategy ATMLP_NIFTY_25300_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.020458389207611553, "take_profit": 0.0485154813103649}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_aa248b6a", "name": "Strategy ATMLP_NIFTY_25000_20250718192943", "description": "Crossover of Strategy ATMLP_NIFTY_25000_20250718192943 and Strategy LC_NIFTY_24300_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_80df858c", "name": "Strategy ATMLP_NIFTY_25300_20250718192943", "description": "Crossover of Strategy ATMLP_NIFTY_25300_20250718192943 and Strategy ATMLC_NIFTY_25100_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.019475627143231464, "take_profit": 0.0509820423476255}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_ab2f29fb", "name": "Strategy LC_NIFTY_25600_20250718192943", "description": "Crossover of Strategy LC_NIFTY_25600_20250718192943 and Strategy ATMLP_NIFTY_25400_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.020158904053924984, "take_profit": 0.052140452643417025}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_ffe8d865", "name": "Strategy ATMLC_NIFTY_25200_20250718192943", "description": "Crossover of Strategy ATMLC_NIFTY_25200_20250718192943 and Strategy ATMLC_NIFTY_25000_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.019944305077677874, "take_profit": 0.05020563027293776}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_7215c3d0", "name": "Strategy LP_NIFTY_25000_20250718192943", "description": "Crossover of Strategy LP_NIFTY_25000_20250718192943 and Strategy ATMLC_NIFTY_24800_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02111338731527049, "take_profit": 0.05306320466616974}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_4286961a", "name": "Strategy ATMLP_NIFTY_25500_20250718192943", "description": "Crossover of Strategy ATMLP_NIFTY_25500_20250718192943 and Strategy LC_NIFTY_25500_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_afb42177", "name": "Strategy LST_NIFTY_25100_24000_20250718192943", "description": "Crossover of Strategy LST_NIFTY_25100_24000_20250718192943 and Strategy LC_NIFTY_24100_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.021712597187356332, "take_profit": 0.03882572973659524}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_208c1e65", "name": "Strategy ATMLP_NIFTY_24500_20250718192943", "description": "Crossover of Strategy ATMLP_NIFTY_24500_20250718192943 and Strategy LST_NIFTY_25200_24100_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.020176929625566963, "take_profit": 0.05070801733595285}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_bf58fec0", "name": "Strategy LP_NIFTY_25500_20250718192943", "description": "Crossover of Strategy LP_NIFTY_25500_20250718192943 and Strategy LS_NIFTY_24900_20250718192943", "parameters": {}, "entry_conditions": ["delta > 0.3"], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_368a862d", "name": "Strategy LS_NIFTY_25600_20250718192943", "description": "Crossover of Strategy LS_NIFTY_25600_20250718192943 and Strategy LC_NIFTY_24200_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_ecd1e247", "name": "Strategy LP_NIFTY_25900_20250718192943", "description": "Crossover of Strategy LP_NIFTY_25900_20250718192943 and Strategy LP_NIFTY_25200_20250718192943", "parameters": {}, "entry_conditions": ["iv_rank < 70"], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.021981338742896057, "take_profit": 0.055891164205913994}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_ad4c6eb6", "name": "Strategy LC_NIFTY_24100_20250718192943", "description": "Crossover of Strategy LC_NIFTY_24100_20250718192943 and Strategy LC_NIFTY_24700_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.01777670632643323, "take_profit": 0.05761993981728798}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_2c26585e", "name": "Strategy ATMLC_NIFTY_25400_20250718192943", "description": "Crossover of Strategy ATMLC_NIFTY_25400_20250718192943 and Strategy LS_NIFTY_25800_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.021550229388924418, "take_profit": 0.04860298840776602}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_9a15e4ac", "name": "Strategy LC_NIFTY_24100_20250718192943", "description": "Crossover of Strategy LC_NIFTY_24100_20250718192943 and Strategy LP_NIFTY_24800_20250718192943", "parameters": {}, "entry_conditions": ["delta > 0.3"], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.01984849787017127, "take_profit": 0.05142475026153163}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_cbcd5882", "name": "Strategy LS_NIFTY_24600_20250718192943", "description": "Crossover of Strategy LS_NIFTY_24600_20250718192943 and Strategy ATMLP_NIFTY_24800_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_13da6988", "name": "Strategy LS_NIFTY_25600_20250718192943", "description": "Crossover of Strategy LS_NIFTY_25600_20250718192943 and Strategy LP_NIFTY_24800_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_fd3e58af", "name": "Strategy ATMLC_NIFTY_24700_20250718192943", "description": "Crossover of Strategy ATMLC_NIFTY_24700_20250718192943 and Strategy LP_NIFTY_25900_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.021196214235841185, "take_profit": 0.0493499755470888}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_05365115", "name": "Strategy ATMLP_NIFTY_25000_20250718192943", "description": "Crossover of Strategy ATMLP_NIFTY_25000_20250718192943 and Strategy LP_NIFTY_25100_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_70585684", "name": "Strategy LS_NIFTY_24100_20250718192943", "description": "Crossover of Strategy LS_NIFTY_24100_20250718192943 and Strategy LP_NIFTY_25000_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_03ac91e3", "name": "Strategy LC_NIFTY_25400_20250718192943", "description": "Crossover of Strategy LC_NIFTY_25400_20250718192943 and Strategy LP_NIFTY_24500_20250718192943", "parameters": {}, "entry_conditions": ["price_change_1d < 0.02"], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.020866972322436932, "take_profit": 0.04998987621426544}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_74689ec3", "name": "Strategy LP_NIFTY_24500_20250718192943", "description": "Crossover of Strategy LP_NIFTY_24500_20250718192943 and Strategy LC_NIFTY_24300_20250718192943", "parameters": {}, "entry_conditions": ["price_change_1d < 0.02"], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.020747780235279775, "take_profit": 0.04986282700357066}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_ee227297", "name": "Strategy LS_NIFTY_25100_20250718192943", "description": "Crossover of Strategy LS_NIFTY_25100_20250718192943 and Strategy LP_NIFTY_25700_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_fe10e20c", "name": "Strategy LST_NIFTY_25100_24200_20250718192943", "description": "Crossover of Strategy LST_NIFTY_25100_24200_20250718192943 and Strategy LC_NIFTY_25600_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.020079452026962492, "take_profit": 0.05107022632170852}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_11dc5023", "name": "Strategy ATMLC_NIFTY_24500_20250718192943", "description": "Crossover of Strategy ATMLC_NIFTY_24500_20250718192943 and Strategy LP_NIFTY_25500_20250718192943", "parameters": {}, "entry_conditions": ["delta > 0.3"], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_bc9d05df", "name": "Strategy LS_NIFTY_25800_20250718192943", "description": "Crossover of Strategy LS_NIFTY_25800_20250718192943 and Strategy LC_NIFTY_25900_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_eb788212", "name": "Strategy LC_NIFTY_24100_20250718192943", "description": "Crossover of Strategy LC_NIFTY_24100_20250718192943 and Strategy ATMLP_NIFTY_25000_20250718192943", "parameters": {}, "entry_conditions": ["delta > 0.3"], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_3d18d154", "name": "Strategy LC_NIFTY_25000_20250718192943", "description": "Crossover of Strategy LC_NIFTY_25000_20250718192943 and Strategy LC_NIFTY_24300_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}]